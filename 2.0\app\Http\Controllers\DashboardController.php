<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function redirect()
    {
        if (Auth::check()) {
            $user = Auth::user();
            switch ($user->role) {
                case 'super_admin':
                case 'admin':
                    return redirect()->route('admin.form-builder');
                case 'user':
                default:
                    return view('dashboard', ['user' => $user]);
            }
        }
        return redirect()->route('login');
    }
}
