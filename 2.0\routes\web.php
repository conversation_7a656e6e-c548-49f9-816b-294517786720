<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FormBuilderController;
use App\Http\Controllers\FormSubmissionController;

// Home
Route::get('/', function () {
    return view('welcome');
});

// Authentication
Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('login', [AuthController::class, 'login']);
Route::get('register', [AuthController::class, 'showRegistrationForm'])->name('register');
Route::post('register', [AuthController::class, 'register']);
Route::post('logout', [AuthController::class, 'logout'])->name('logout');

// Dashboard (role-based redirect)
Route::get('/dashboard', [DashboardController::class, 'redirect'])->name('dashboard');

// Form Builder (admin only)
Route::middleware(['auth', 'role:admin'])->prefix('admin')->group(function () {
    Route::get('form-builder', [FormBuilderController::class, 'index'])->name('admin.form-builder');
    Route::get('form-builder/create', [FormBuilderController::class, 'create'])->name('admin.form-builder.create');
    Route::post('form-builder', [FormBuilderController::class, 'store'])->name('admin.form-builder.store');
    Route::get('form-builder/{id}/edit', [FormBuilderController::class, 'edit'])->name('admin.form-builder.edit');
    Route::post('form-builder/{id}', [FormBuilderController::class, 'update'])->name('admin.form-builder.update');
    Route::delete('form-builder/{id}', [FormBuilderController::class, 'destroy'])->name('admin.form-builder.destroy');
});

// Form Submission (user)
Route::middleware(['auth'])->group(function () {
    Route::get('forms', [FormSubmissionController::class, 'index'])->name('forms.index');
    Route::get('forms/{id}', [FormSubmissionController::class, 'show'])->name('forms.show');
    Route::post('forms/{id}/submit', [FormSubmissionController::class, 'submit'])->name('forms.submit');
    Route::post('forms/{id}/upload', [FormSubmissionController::class, 'upload'])->name('forms.upload');
});
