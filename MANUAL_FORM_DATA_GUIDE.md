# Manual Form Data Entry Guide

Since form editing is not working properly, this guide provides multiple ways to manually add form data to your application.

## Available Methods

### 1. Web Interface (Recommended for Non-Technical Users)

**URL:** `/manual-forms/interface`

**Features:**
- User-friendly web interface
- Select any existing form
- Fill out form fields dynamically
- Bulk upload via JSON
- View recent submissions
- Real-time validation

**How to Use:**
1. Navigate to `/manual-forms/interface` in your browser
2. Select a form from the dropdown
3. Fill out the form fields that appear
4. Click "Add Form Data" to submit
5. View recent submissions at the bottom

### 2. API Endpoints (For Developers/Scripts)

#### Add Single Form Data
```http
POST /manual-forms/add-data
Content-Type: application/json

{
    "form_id": 1,
    "user_id": 2,
    "form_data": {
        "field1": "value1",
        "field2": "value2"
    }
}
```

#### Bulk Add Form Data
```http
POST /manual-forms/bulk-add
Content-Type: application/json

{
    "form_id": 1,
    "submissions": [
        {
            "user_id": 1,
            "form_data": {
                "field1": "value1",
                "field2": "value2"
            }
        },
        {
            "user_id": 2,
            "form_data": {
                "field1": "value3",
                "field2": "value4"
            }
        }
    ]
}
```

#### Get Form Structure
```http
GET /manual-forms/{formId}/structure
```

#### Get Form Submissions
```http
GET /manual-forms/{formId}/submissions
```

### 3. Command Line (For Server Administrators)

#### Add Single Form Data
```bash
# Interactive mode
php artisan form:add-data 1

# With JSON data
php artisan form:add-data 1 2 --data='{"name":"John","email":"<EMAIL>"}'

# From JSON file
php artisan form:add-data 1 2 --file=/path/to/data.json
```

#### Bulk Add Form Data
```bash
php artisan form:add-data 1 --bulk=/path/to/bulk-data.json
```

**Bulk JSON Format:**
```json
[
    {
        "user_id": 1,
        "form_data": {
            "name": "John Doe",
            "email": "<EMAIL>"
        }
    },
    {
        "user_id": 2,
        "form_data": {
            "name": "Jane Smith",
            "email": "<EMAIL>"
        }
    }
]
```

### 4. Direct Database Insertion (Advanced Users)

If you need to insert data directly into the database:

1. **Find your form:**
   ```sql
   SELECT id, form_name FROM form_builders;
   ```

2. **Check the table structure:**
   ```sql
   DESCRIBE form_your_form_name;
   ```

3. **Insert data:**
   ```sql
   INSERT INTO form_your_form_name (user_id, field1, field2, created_at, updated_at) 
   VALUES (1, 'value1', 'value2', NOW(), NOW());
   ```

## Form Data Structure

### Understanding Form Fields

Your forms support these field types:
- **text**: String values
- **email**: Email addresses
- **number**: Numeric values
- **textarea**: Long text
- **select**: Dropdown selections
- **checkbox**: Boolean (single) or array (multiple)
- **radio**: Single selection
- **file**: File paths/names

### Checkbox Handling

- **Single checkbox**: Use `"1"` for checked, `"0"` for unchecked
- **Multiple checkboxes**: Use array of selected values: `["option1", "option3"]`

### Example Form Data

```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": "30",
    "bio": "Software developer with 5 years experience",
    "country": "USA",
    "skills": ["PHP", "JavaScript", "Python"],
    "newsletter": "1",
    "terms": "1"
}
```

## Troubleshooting

### Common Issues

1. **Form ID not found**
   - Check available forms: `GET /manual-forms/`
   - Verify form exists in `form_builders` table

2. **User ID not found**
   - Check available users in `users` table
   - Use existing user ID or leave empty to use current user

3. **Invalid JSON format**
   - Validate JSON using online tools
   - Check for trailing commas, quotes, brackets

4. **Table doesn't exist**
   - Form tables are created automatically on first submission
   - Check if form structure is valid

### Logs

Check Laravel logs for detailed error information:
```bash
tail -f storage/logs/laravel.log
```

## Security Notes

- All endpoints require authentication
- CSRF protection is enabled for web interface
- Validate all input data before submission
- Use appropriate user permissions

## Database Tables

- **form_builders**: Stores form definitions
- **form_{form_name}**: Dynamic tables for each form's submissions
- **users**: User information

## Support

If you encounter issues:
1. Check the Laravel logs
2. Verify form structure in database
3. Test with simple data first
4. Use the web interface for easier debugging
