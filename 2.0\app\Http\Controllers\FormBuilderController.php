<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FormBuilder;

class FormBuilderController extends Controller
{
    public function index()
    {
        $forms = FormBuilder::all();
        return view('form_builder.index', compact('forms'));
    }
    public function create()
    {
        return view('form_builder.create');
    }
    public function store(Request $request)
    {
        // Validate and store form
        $form = FormBuilder::create($request->all());
        return redirect()->route('admin.form-builder');
    }
    public function edit($id)
    {
        $form = FormBuilder::findOrFail($id);
        return view('form_builder.edit', compact('form'));
    }
    public function update(Request $request, $id)
    {
        $form = FormBuilder::findOrFail($id);
        $form->update($request->all());
        return redirect()->route('admin.form-builder');
    }
    public function destroy($id)
    {
        $form = FormBuilder::findOrFail($id);
        $form->delete();
        return redirect()->route('admin.form-builder');
    }
}
