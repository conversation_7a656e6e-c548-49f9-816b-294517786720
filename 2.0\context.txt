Project Context for 2.0 (Simplified <PERSON><PERSON> Project)

- Only one web.php route file is used for all routes.
- Authentication is handled by a custom AuthController (login, register, logout).
- DashboardController handles role-based dashboard redirection.
- FormBuilderController allows admin to create, edit, and delete forms.
- FormSubmissionController allows users to submit forms and upload files.
- File upload logic is robust and uses Laravel's storage system.
- Database structure includes users, form_builders, form_submissions, states, and districts tables.
- Views are minimal and located in resources/views/auth and resources/views/.
- All assets (css, js, images) are reused from the original project and located in public/assets, public/js, public/images.
- .env is configured to use the same database as the original project.
- Migrations are kept clean and minimal for easy maintenance.
- This context file should be updated if the structure or logic changes in the future.
