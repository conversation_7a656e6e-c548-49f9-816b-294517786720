@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Visit Planning</span>
                    <button type="button" class="btn btn-primary" id="newVisitBtn">Plan New Visit</button>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Plan visits to districts within your zone.</li>
                            <li>After the visit, submit a report with observations, recommendations, and photos.</li>
                            <li>Use the GIS location feature to capture the exact location of your visit.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>District</th>
                                    <th>Type</th>
                                    <th>Location</th>
                                    <th>Visit Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="visitsTableBody">
                                <!-- Visits will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New/Edit Visit Modal -->
<div class="modal fade" id="visitModal" tabindex="-1" aria-labelledby="visitModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="visitModalLabel">Plan New Visit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="visitForm">
                    <input type="hidden" id="visitId" name="id">

                    <div class="mb-3">
                        <label for="district_id" class="form-label">District</label>
                        <select class="form-select" id="district_id" name="district_id" required>
                            <option value="">Select District</option>
                            <!-- Districts will be loaded here -->
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">Visit Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Visit Type</label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="">Select Type</option>
                            <option value="training">Training Programme</option>
                            <option value="field_visit">Field Visit</option>
                            <option value="demonstration">Demonstration</option>
                            <option value="awareness">Awareness Program</option>
                            <option value="monitoring">Monitoring Visit</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="location" name="location" required>
                    </div>

                    <div class="mb-3">
                        <label for="visit_date" class="form-label">Visit Date</label>
                        <input type="datetime-local" class="form-control" id="visit_date" name="visit_date" required>
                    </div>

                    <div class="mb-3">
                        <label for="purpose" class="form-label">Purpose of Visit</label>
                        <textarea class="form-control" id="purpose" name="purpose" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveVisitBtn">Save Visit</button>
            </div>
        </div>
    </div>
</div>

<!-- View Visit Modal -->
<div class="modal fade" id="viewVisitModal" tabindex="-1" aria-labelledby="viewVisitModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewVisitModalLabel">Visit Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="viewVisitBody">
                <!-- Visit details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">Submit Visit Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reportForm" enctype="multipart/form-data">
                    <input type="hidden" id="reportVisitId" name="visit_id">

                    <div class="mb-3">
                        <label for="observations" class="form-label">Observations</label>
                        <textarea class="form-control" id="observations" name="observations" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="recommendations" class="form-label">Recommendations</label>
                        <textarea class="form-control" id="recommendations" name="recommendations" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="photos" class="form-label">Photos (Max 4)</label>
                        <input type="file" class="form-control" id="photos" name="photos[]" accept="image/jpeg,image/png,image/jpg" multiple required>
                        <div class="form-text">Upload up to 4 photos (JPEG, PNG, JPG) of max 2MB each.</div>
                    </div>

                    <div class="mb-3">
                        <label for="gis_location" class="form-label">GIS Location</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="gis_location" name="gis_location" placeholder="Latitude, Longitude">
                            <button class="btn btn-outline-secondary" type="button" id="captureLocationBtn">Capture Location</button>
                        </div>
                        <div class="form-text">Click "Capture Location" to automatically get your current location.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitReportBtn">Submit Report</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize modals
        const visitModal = new bootstrap.Modal(document.getElementById('visitModal'));
        const viewVisitModal = new bootstrap.Modal(document.getElementById('viewVisitModal'));
        const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));

        // Get DOM elements
        const newVisitBtn = document.getElementById('newVisitBtn');
        const saveVisitBtn = document.getElementById('saveVisitBtn');
        const submitReportBtn = document.getElementById('submitReportBtn');
        const captureLocationBtn = document.getElementById('captureLocationBtn');
        const visitsTableBody = document.getElementById('visitsTableBody');
        const visitForm = document.getElementById('visitForm');
        const reportForm = document.getElementById('reportForm');

        // Load visits
        loadVisits();

        // Load districts for the dropdown
        loadDistricts();

        // Set minimum date for visit date input
        const visitDateInput = document.getElementById('visit_date');
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const minDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
        visitDateInput.min = minDateTime;

        // Event listeners
        newVisitBtn.addEventListener('click', function() {
            resetVisitForm();
            document.getElementById('visitModalLabel').textContent = 'Plan New Visit';
            visitModal.show();
        });

        saveVisitBtn.addEventListener('click', function() {
            if (!visitForm.checkValidity()) {
                visitForm.reportValidity();
                return;
            }

            saveVisit();
        });

        submitReportBtn.addEventListener('click', function() {
            if (!reportForm.checkValidity()) {
                reportForm.reportValidity();
                return;
            }

            submitReport();
        });

        captureLocationBtn.addEventListener('click', function() {
            captureLocation();
        });

        // Functions
        function loadVisits() {
            fetch('{{ route("zonal-coordinator.visits.get-visits") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    visitsTableBody.innerHTML = '';

                    if (data.length === 0) {
                        visitsTableBody.innerHTML = '<tr><td colspan="7" class="text-center">No visits found</td></tr>';
                        return;
                    }

                    data.forEach(visit => {
                        const row = document.createElement('tr');

                        // Format date
                        const visitDate = new Date(visit.visit_date);
                        const formattedDate = visitDate.toLocaleDateString() + ' ' + visitDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                        // Format type
                        let typeText = '';
                        switch(visit.type) {
                            case 'training': typeText = 'Training Programme'; break;
                            case 'field_visit': typeText = 'Field Visit'; break;
                            case 'demonstration': typeText = 'Demonstration'; break;
                            case 'awareness': typeText = 'Awareness Program'; break;
                            case 'monitoring': typeText = 'Monitoring Visit'; break;
                        }

                        // Format status
                        let statusBadge = '';
                        switch(visit.status) {
                            case 'planned':
                                statusBadge = '<span class="badge bg-warning">Planned</span>';
                                break;
                            case 'completed':
                                statusBadge = '<span class="badge bg-success">Completed</span>';
                                break;
                            case 'cancelled':
                                statusBadge = '<span class="badge bg-danger">Cancelled</span>';
                                break;
                        }

                        // Action buttons
                        let actionButtons = `<button class="btn btn-sm btn-info view-visit" data-id="${visit.id}">View</button> `;

                        if (visit.status === 'planned') {
                            actionButtons += `
                                <button class="btn btn-sm btn-primary edit-visit" data-id="${visit.id}">Edit</button>
                                <button class="btn btn-sm btn-danger cancel-visit" data-id="${visit.id}">Cancel</button>
                            `;

                            // Check if visit date has passed
                            if (new Date(visit.visit_date) <= new Date()) {
                                actionButtons += `<button class="btn btn-sm btn-success report-visit" data-id="${visit.id}">Submit Report</button>`;
                            }
                        }

                        row.innerHTML = `
                            <td>${visit.title}</td>
                            <td>${visit.district ? visit.district.district + ', ' + visit.district.state : 'N/A'}</td>
                            <td>${typeText}</td>
                            <td>${visit.location}</td>
                            <td>${formattedDate}</td>
                            <td>${statusBadge}</td>
                            <td>${actionButtons}</td>
                        `;

                        visitsTableBody.appendChild(row);
                    });

                    // Add event listeners to buttons
                    document.querySelectorAll('.view-visit').forEach(btn => {
                        btn.addEventListener('click', function() {
                            viewVisit(this.getAttribute('data-id'));
                        });
                    });

                    document.querySelectorAll('.edit-visit').forEach(btn => {
                        btn.addEventListener('click', function() {
                            editVisit(this.getAttribute('data-id'));
                        });
                    });

                    document.querySelectorAll('.cancel-visit').forEach(btn => {
                        btn.addEventListener('click', function() {
                            cancelVisit(this.getAttribute('data-id'));
                        });
                    });

                    document.querySelectorAll('.report-visit').forEach(btn => {
                        btn.addEventListener('click', function() {
                            showReportModal(this.getAttribute('data-id'));
                        });
                    });
                })
                .catch(error => {
                    console.error('Error loading visits:', error);
                    alert('Error loading visits. Please try again later.');
                });
        }

        function loadDistricts() {
            fetch('{{ route("zonal-coordinator.districts.get-districts") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    const districtSelect = document.getElementById('district_id');
                    districtSelect.innerHTML = '<option value="">Select District</option>';

                    data.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = `${district.district}, ${district.state} (${district.status})`;
                        districtSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading districts:', error);
                    alert('Error loading districts. Please try again later.');
                });
        }

        function resetVisitForm() {
            visitForm.reset();
            document.getElementById('visitId').value = '';

            // Set default date to tomorrow
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(10, 0, 0, 0); // Set to 10:00 AM

            const year = tomorrow.getFullYear();
            const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
            const day = String(tomorrow.getDate()).padStart(2, '0');
            const hours = String(tomorrow.getHours()).padStart(2, '0');
            const minutes = String(tomorrow.getMinutes()).padStart(2, '0');
            const defaultDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;

            document.getElementById('visit_date').value = defaultDateTime;
        }

        function saveVisit() {
            const formData = new FormData(visitForm);
            const visitId = document.getElementById('visitId').value;
            const jsonData = {};

            formData.forEach((value, key) => {
                jsonData[key] = value;
            });

            console.log('Visit form data:', jsonData);

            const url = visitId ?
                `{{ route("zonal-coordinator.visits.update", ["id" => ":id"]) }}`.replace(':id', visitId) :
                '{{ route("zonal-coordinator.visits.create") }}';

            const method = visitId ? 'PUT' : 'POST';

            console.log('Submitting to URL:', url, 'Method:', method);

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(jsonData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => Promise.reject(err));
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    alert('Error: ' + data.error);
                    return;
                }

                alert(data.message);
                visitModal.hide();
                loadVisits();
            })
            .catch(error => {
                console.error('Error saving visit:', error);
                let errorMessage = 'Error saving visit. Please try again later.';
                if (error.error) {
                    errorMessage = 'Error: ' + error.error;
                } else if (error.message) {
                    errorMessage = 'Error: ' + error.message;
                }
                alert(errorMessage);
            });
        }

        function viewVisit(id) {
            fetch(`{{ route("zonal-coordinator.visits.visit", ["id" => ":id"]) }}`.replace(':id', id))
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Format date
                    const visitDate = new Date(data.visit_date);
                    const formattedDate = visitDate.toLocaleDateString() + ' ' + visitDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                    // Format type
                    let typeText = '';
                    switch(data.type) {
                        case 'training': typeText = 'Training Programme'; break;
                        case 'field_visit': typeText = 'Field Visit'; break;
                        case 'demonstration': typeText = 'Demonstration'; break;
                        case 'awareness': typeText = 'Awareness Program'; break;
                        case 'monitoring': typeText = 'Monitoring Visit'; break;
                    }

                    // Format status
                    let statusText = '';
                    switch(data.status) {
                        case 'planned': statusText = '<span class="badge bg-warning">Planned</span>'; break;
                        case 'completed': statusText = '<span class="badge bg-success">Completed</span>'; break;
                        case 'cancelled': statusText = '<span class="badge bg-danger">Cancelled</span>'; break;
                    }

                    let html = `
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>Title:</strong> ${data.title}</p>
                                <p><strong>District:</strong> ${data.district ? data.district.district + ', ' + data.district.state : 'N/A'}</p>
                                <p><strong>Type:</strong> ${typeText}</p>
                                <p><strong>Location:</strong> ${data.location}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Visit Date:</strong> ${formattedDate}</p>
                                <p><strong>Status:</strong> ${statusText}</p>
                                <p><strong>Purpose:</strong> ${data.purpose || 'Not specified'}</p>
                            </div>
                        </div>
                    `;

                    // Add report details if completed
                    if (data.status === 'completed') {
                        html += `
                            <hr>
                            <h5>Visit Report</h5>
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <p><strong>Observations:</strong></p>
                                    <p>${data.observations}</p>
                                    <p><strong>Recommendations:</strong></p>
                                    <p>${data.recommendations}</p>
                                </div>
                            </div>
                        `;

                        // Add photos if available
                        if (data.photos && data.photos.length > 0) {
                            html += '<h5>Visit Photos</h5><div class="row mb-3">';

                            data.photos.forEach(photo => {
                                html += `
                                    <div class="col-md-3 mb-2">
                                        <img src="{{ asset('storage') }}/${photo}" class="img-thumbnail" alt="Visit Photo">
                                    </div>
                                `;
                            });

                            html += '</div>';
                        }

                        // Add GIS location if available
                        if (data.gis_location) {
                            html += `<p><strong>GIS Location:</strong> ${data.gis_location}</p>`;
                        }
                    }

                    document.getElementById('viewVisitBody').innerHTML = html;
                    viewVisitModal.show();
                })
                .catch(error => {
                    console.error('Error loading visit details:', error);
                    alert('Error loading visit details. Please try again later.');
                });
        }

        function editVisit(id) {
            fetch(`{{ route("zonal-coordinator.visits.visit", ["id" => ":id"]) }}`.replace(':id', id))
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Fill the form
                    document.getElementById('visitId').value = data.id;
                    document.getElementById('district_id').value = data.district_id;
                    document.getElementById('title').value = data.title;
                    document.getElementById('type').value = data.type;
                    document.getElementById('location').value = data.location;

                    // Format date for datetime-local input
                    const visitDate = new Date(data.visit_date);
                    const formattedDate = visitDate.toISOString().slice(0, 16);
                    document.getElementById('visit_date').value = formattedDate;

                    document.getElementById('purpose').value = data.purpose || '';

                    document.getElementById('visitModalLabel').textContent = 'Edit Visit';
                    visitModal.show();
                })
                .catch(error => {
                    console.error('Error loading visit details:', error);
                    alert('Error loading visit details. Please try again later.');
                });
        }

        function cancelVisit(id) {
            if (!confirm('Are you sure you want to cancel this visit?')) {
                return;
            }

            fetch(`{{ route("zonal-coordinator.visits.cancel", ["id" => ":id"]) }}`.replace(':id', id), {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                    return;
                }

                alert(data.message);
                loadVisits();
            })
            .catch(error => {
                console.error('Error cancelling visit:', error);
                alert('Error cancelling visit. Please try again later.');
            });
        }

        function showReportModal(id) {
            document.getElementById('reportVisitId').value = id;
            reportForm.reset();
            reportModal.show();
        }

        function submitReport() {
            const visitId = document.getElementById('reportVisitId').value;
            const formData = new FormData(reportForm);

            fetch(`{{ route("zonal-coordinator.visits.submit-report", ["id" => ":id"]) }}`.replace(':id', visitId), {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                    return;
                }

                alert(data.message);
                reportModal.hide();
                loadVisits();
            })
            .catch(error => {
                console.error('Error submitting report:', error);
                alert('Error submitting report. Please try again later.');
            });
        }

        function captureLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        document.getElementById('gis_location').value = `${lat}, ${lng}`;
                    },
                    function(error) {
                        console.error('Error getting location:', error);
                        alert('Error getting location. Please try again or enter manually.');
                    }
                );
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        }
    });
</script>
@endsection
