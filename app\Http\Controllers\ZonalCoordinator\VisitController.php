<?php

namespace App\Http\Controllers\ZonalCoordinator;

use App\Http\Controllers\Controller;
use App\Models\Visit;
use App\Models\ZonalCoordinator;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class VisitController extends Controller
{
    /**
     * Display the visits page.
     */
    public function index()
    {
        return view('zonal-coordinator.visits.index');
    }

    /**
     * Get all visits for the current zonal coordinator.
     */
    public function getVisits()
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get visits for this coordinator
            $visits = Visit::where('zonal_coordinator_id', $coordinator->id)
                ->with('district:id,state,district')
                ->orderBy('visit_date', 'desc')
                ->get();

            return response()->json($visits);
        } catch (\Exception $e) {
            Log::error('Error getting visits: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get visits'], 500);
        }
    }

    /**
     * Get a specific visit.
     */
    public function getVisit($id)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get the visit
            $visit = Visit::where('id', $id)
                ->where('zonal_coordinator_id', $coordinator->id)
                ->with('district:id,state,district')
                ->first();

            if (!$visit) {
                return response()->json(['error' => 'Visit not found'], 404);
            }

            return response()->json($visit);
        } catch (\Exception $e) {
            Log::error('Error getting visit: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get visit'], 500);
        }
    }

    /**
     * Create a new visit.
     */
    public function createVisit(Request $request)
    {
        Log::info('Visit creation request received', [
            'user_id' => auth()->id(),
            'request_data' => $request->all()
        ]);

        try {
            $request->validate([
                'district_id' => 'required|exists:state_data,id',
                'title' => 'required|string|max:255',
                'location' => 'required|string|max:255',
                'visit_date' => 'required|date|after_or_equal:today',
                'type' => 'required|in:training,field_visit,demonstration,awareness,monitoring',
                'purpose' => 'nullable|string',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Visit creation validation failed', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['error' => 'Validation failed: ' . implode(', ', array_map(function($arr) {
                return implode(', ', $arr);
            }, $e->errors()))], 422);
        }

        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                Log::error('Zonal coordinator not found for user', ['user_id' => auth()->id()]);
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            Log::info('Found zonal coordinator', ['coordinator_id' => $coordinator->id]);

            // Verify the district is assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            Log::info('Coordinator districts', ['district_ids' => $districtIds->toArray()]);

            if (!$districtIds->contains($request->district_id)) {
                Log::warning('District not assigned to coordinator', [
                    'coordinator_id' => $coordinator->id,
                    'requested_district_id' => $request->district_id,
                    'assigned_districts' => $districtIds->toArray()
                ]);
                return response()->json(['error' => 'District not assigned to this coordinator'], 403);
            }

            // Create the visit
            $visit = Visit::create([
                'zonal_coordinator_id' => $coordinator->id,
                'district_id' => $request->district_id,
                'title' => $request->title,
                'location' => $request->location,
                'visit_date' => $request->visit_date,
                'type' => $request->type,
                'purpose' => $request->purpose,
                'status' => 'planned',
            ]);

            Log::info('Visit created successfully', ['visit_id' => $visit->id]);

            return response()->json([
                'message' => 'Visit created successfully',
                'visit' => $visit
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating visit: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);
            return response()->json(['error' => 'Failed to create visit: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Update a visit.
     */
    public function updateVisit(Request $request, $id)
    {
        $request->validate([
            'district_id' => 'required|exists:state_data,id',
            'title' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'visit_date' => 'required|date',
            'type' => 'required|in:training,field_visit,demonstration,awareness,monitoring',
            'purpose' => 'nullable|string',
        ]);

        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get the visit
            $visit = Visit::where('id', $id)
                ->where('zonal_coordinator_id', $coordinator->id)
                ->first();

            if (!$visit) {
                return response()->json(['error' => 'Visit not found'], 404);
            }

            // Only allow updates if the visit is still in 'planned' status
            if ($visit->status !== 'planned') {
                return response()->json(['error' => 'Cannot update a completed or cancelled visit'], 403);
            }

            // Verify the district is assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            if (!$districtIds->contains($request->district_id)) {
                return response()->json(['error' => 'District not assigned to this coordinator'], 403);
            }

            // Update the visit
            $visit->update([
                'district_id' => $request->district_id,
                'title' => $request->title,
                'location' => $request->location,
                'visit_date' => $request->visit_date,
                'type' => $request->type,
                'purpose' => $request->purpose,
            ]);

            return response()->json([
                'message' => 'Visit updated successfully',
                'visit' => $visit
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating visit: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update visit'], 500);
        }
    }

    /**
     * Cancel a visit.
     */
    public function cancelVisit($id)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get the visit
            $visit = Visit::where('id', $id)
                ->where('zonal_coordinator_id', $coordinator->id)
                ->first();

            if (!$visit) {
                return response()->json(['error' => 'Visit not found'], 404);
            }

            // Only allow cancellation if the visit is still in 'planned' status
            if ($visit->status !== 'planned') {
                return response()->json(['error' => 'Cannot cancel a completed or already cancelled visit'], 403);
            }

            // Cancel the visit
            $visit->update([
                'status' => 'cancelled'
            ]);

            return response()->json([
                'message' => 'Visit cancelled successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error cancelling visit: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to cancel visit'], 500);
        }
    }

    /**
     * Submit visit report.
     */
    public function submitReport(Request $request, $id)
    {
        $request->validate([
            'observations' => 'required|string',
            'recommendations' => 'required|string',
            'photos' => 'required|array|min:1|max:4',
            'photos.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'gis_location' => 'nullable|string|max:255',
        ]);

        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get the visit
            $visit = Visit::where('id', $id)
                ->where('zonal_coordinator_id', $coordinator->id)
                ->first();

            if (!$visit) {
                return response()->json(['error' => 'Visit not found'], 404);
            }

            // Only allow reporting if the visit is in 'planned' status
            if ($visit->status !== 'planned') {
                return response()->json(['error' => 'Cannot submit report for a cancelled visit'], 403);
            }

            // Check if the visit date has passed
            if ($visit->visit_date > now()) {
                return response()->json(['error' => 'Cannot submit report before the visit date'], 403);
            }

            // Upload photos
            $photosPaths = [];
            if ($request->hasFile('photos')) {
                foreach ($request->file('photos') as $photo) {
                    $path = $photo->store('visit-photos', 'public');
                    $photosPaths[] = $path;
                }
            }

            // Update visit with report data
            $visit->update([
                'observations' => $request->observations,
                'recommendations' => $request->recommendations,
                'photos' => $photosPaths,
                'gis_location' => $request->gis_location,
                'status' => 'completed'
            ]);

            return response()->json([
                'message' => 'Visit report submitted successfully',
                'visit' => $visit
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting visit report: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to submit visit report'], 500);
        }
    }
}
