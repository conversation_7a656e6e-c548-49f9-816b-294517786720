@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">{{ __('Super Admin Dashboard') }}</div>

                <div class="card-body">
                    <h2 class="text-center mb-4">Welcome Super Admin</h2>

                    <div class="alert alert-success" role="alert">
                        You are logged in as a Super Administrator!
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">District Management</div>
                                <div class="card-body">
                                    <p>Manage districts and their Pre-Cocoon/Post-Cocoon designation.</p>
                                    <a href="{{ route('super-admin.district-management.index') }}" class="btn btn-primary">Manage Districts</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Scientist Management</div>
                                <div class="card-body">
                                    <p>Manage scientists and their district assignments.</p>
                                    <a href="{{ route('super-admin.scientist-management.index') }}" class="btn btn-primary">Manage Scientists</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">District Coordinator Management</div>
                                <div class="card-body">
                                    <p>Manage District  Coordinators with multiple state & district mapping.</p>
                                    <a href="{{ route('super-admin.district-state-coordinator.index') }}" class="btn btn-primary">Manage Coordinators</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Zonal/State Coordinator/Facilitator Management</div>
                                <div class="card-body">
                                    <p>Manage Zonal/State Coordinator/Facilitator  with multiple district mapping in the same state.</p>
                                    <a href="{{ route('super-admin.zonal-coordinator.index') }}" class="btn btn-primary">Manage Coordinators</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Event Management</div>
                                <div class="card-body">
                                    <p>Manage events and track participation metrics.</p>
                                    <a href="{{ route('super-admin.event-management.index') }}" class="btn btn-primary">Manage Events</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Scientist Feedback</div>
                                <div class="card-body">
                                    <p>Record and manage feedback for scientists.</p>
                                    <a href="{{ route('super-admin.scientist-feedback.index') }}" class="btn btn-primary">Manage Feedback</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        {{-- <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Performance Dashboard</div>
                                <div class="card-body">
                                    <p>View comprehensive performance metrics and statistics.</p>
                                    <a href="{{ route('super-admin.performance-dashboard.index') }}" class="btn btn-primary">View Dashboard</a>
                                </div>
                            </div>
                        </div> --}}
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Custom Form Builder</div>
                                <div class="card-body">
                                    <p>Create and manage custom forms for different profiles.</p>
                                    <a href="{{ route('super-admin.form-builder.index') }}" class="btn btn-primary">Manage Forms</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
