<?php

namespace App\Http\Controllers;

use App\Models\FormBuilder;
use App\Models\FormSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class ManualFormDataController extends Controller
{
    /**
     * Manually add form data to a specific form
     */
    public function addFormData(Request $request)
    {
        try {
            $formId = $request->input('form_id');
            $userId = $request->input('user_id', Auth::id());
            $formData = $request->input('form_data', []);

            // Validate required fields
            if (!$formId) {
                return response()->json(['error' => 'Form ID is required'], 422);
            }

            if (!$userId) {
                return response()->json(['error' => 'User ID is required'], 422);
            }

            if (empty($formData)) {
                return response()->json(['error' => 'Form data is required'], 422);
            }

            // Get the form
            $form = FormBuilder::findOrFail($formId);
            $formName = $form->form_name;
            $tableName = 'form_' . Str::slug($formName, '_');

            Log::info('Manual form data insertion', [
                'form_id' => $formId,
                'form_name' => $formName,
                'table_name' => $tableName,
                'user_id' => $userId
            ]);

            // Create the form submission using the existing method
            $submissionId = FormSubmission::createSubmission($formId, $userId, $formData);

            if (!$submissionId) {
                throw new \Exception('Failed to create form submission');
            }

            return response()->json([
                'message' => 'Form data added successfully',
                'submission_id' => $submissionId,
                'table_name' => $tableName
            ]);

        } catch (\Exception $e) {
            Log::error('Error adding manual form data: ' . $e->getMessage(), [
                'exception' => $e,
                'request_data' => $request->all()
            ]);

            return response()->json([
                'error' => 'Failed to add form data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all forms available for manual data entry
     */
    public function getForms()
    {
        try {
            $forms = FormBuilder::select('id', 'form_name', 'target_user', 'form_structure', 'created_at')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'forms' => $forms
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching forms: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch forms'], 500);
        }
    }

    /**
     * Get form structure for a specific form
     */
    public function getFormStructure($formId)
    {
        try {
            $form = FormBuilder::findOrFail($formId);
            
            return response()->json([
                'form' => $form,
                'structure' => $form->form_structure
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching form structure: ' . $e->getMessage());
            return response()->json(['error' => 'Form not found'], 404);
        }
    }

    /**
     * Bulk insert multiple form submissions
     */
    public function bulkAddFormData(Request $request)
    {
        try {
            $formId = $request->input('form_id');
            $submissions = $request->input('submissions', []);

            if (!$formId) {
                return response()->json(['error' => 'Form ID is required'], 422);
            }

            if (empty($submissions)) {
                return response()->json(['error' => 'Submissions data is required'], 422);
            }

            $successCount = 0;
            $errors = [];

            foreach ($submissions as $index => $submission) {
                try {
                    $userId = $submission['user_id'] ?? Auth::id();
                    $formData = $submission['form_data'] ?? [];

                    $submissionId = FormSubmission::createSubmission($formId, $userId, $formData);
                    
                    if ($submissionId) {
                        $successCount++;
                    } else {
                        $errors[] = "Failed to create submission at index {$index}";
                    }

                } catch (\Exception $e) {
                    $errors[] = "Error at index {$index}: " . $e->getMessage();
                }
            }

            return response()->json([
                'message' => "Successfully added {$successCount} submissions",
                'success_count' => $successCount,
                'total_count' => count($submissions),
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            Log::error('Error in bulk form data insertion: ' . $e->getMessage());
            return response()->json(['error' => 'Bulk insertion failed'], 500);
        }
    }

    /**
     * Get submissions for a specific form
     */
    public function getFormSubmissions($formId)
    {
        try {
            $form = FormBuilder::findOrFail($formId);
            $submissions = FormSubmission::getSubmissionsByFormId($formId);

            return response()->json([
                'form' => $form,
                'submissions' => $submissions
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching form submissions: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch submissions'], 500);
        }
    }
}
