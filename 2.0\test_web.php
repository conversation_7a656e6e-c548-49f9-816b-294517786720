<?php

// Test web application functionality
echo "Testing Laravel 2.0 Application\n";
echo "===============================\n\n";

// Test 1: Home page
echo "1. Testing home page...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost:8000/");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    echo "   ✓ Home page accessible\n";
} else {
    echo "   ✗ Home page failed (HTTP $httpCode)\n";
}

// Test 2: Login page
echo "\n2. Testing login page...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost:8000/login");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    echo "   ✓ Login page accessible\n";
} else {
    echo "   ✗ Login page failed (HTTP $httpCode)\n";
}

// Test 3: Register page
echo "\n3. Testing register page...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost:8000/register");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    echo "   ✓ Register page accessible\n";
} else {
    echo "   ✗ Register page failed (HTTP $httpCode)\n";
}

// Test 4: Dashboard (should redirect to login)
echo "\n4. Testing dashboard redirect...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost:8000/dashboard");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 302) {
    echo "   ✓ Dashboard redirects to login (as expected)\n";
} else {
    echo "   ✗ Dashboard redirect failed (HTTP $httpCode)\n";
}

echo "\nWeb application tests completed!\n";
echo "You can now test manually by visiting:\n";
echo "- Home: http://localhost:8000/\n";
echo "- Login: http://localhost:8000/login\n";
echo "- Register: http://localhost:8000/register\n";
echo "\nSuper Admin Credentials:\n";
echo "- Email: <EMAIL>\n";
echo "- Password: BusOnATable@161619?\n";
