<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FormSubmission;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class FormSubmissionController extends Controller
{
    public function index()
    {
        $submissions = FormSubmission::where('user_id', Auth::id())->get();
        return view('form_submission.index', compact('submissions'));
    }
    public function show($id)
    {
        $submission = FormSubmission::findOrFail($id);
        return view('form_submission.show', compact('submission'));
    }
    public function submit(Request $request, $id)
    {
        // Validate and store form submission
        $submission = FormSubmission::create([
            'form_builder_id' => $id,
            'user_id' => Auth::id(),
            'form_data' => json_encode($request->except(['_token', 'files'])),
            'file_uploads' => json_encode([]),
        ]);
        return redirect()->route('forms.index');
    }
    public function upload(Request $request, $id)
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
        ]);
        $path = $request->file('file')->store('uploads', 'public');
        // Optionally associate with submission
        return response()->json(['path' => $path]);
    }
}
