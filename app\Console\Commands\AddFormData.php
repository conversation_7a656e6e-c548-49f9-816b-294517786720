<?php

namespace App\Console\Commands;

use App\Models\FormBuilder;
use App\Models\FormSubmission;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AddFormData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'form:add-data 
                            {form_id : The ID of the form}
                            {user_id? : The ID of the user (optional, defaults to first user)}
                            {--data= : JSON string of form data}
                            {--file= : Path to JSON file containing form data}
                            {--bulk= : Path to JSON file containing array of submissions}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manually add form data to a specific form';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $formId = $this->argument('form_id');
        $userId = $this->argument('user_id');
        
        // Validate form exists
        $form = FormBuilder::find($formId);
        if (!$form) {
            $this->error("Form with ID {$formId} not found.");
            return 1;
        }

        // Get user ID if not provided
        if (!$userId) {
            $user = User::first();
            if (!$user) {
                $this->error("No users found in the system.");
                return 1;
            }
            $userId = $user->id;
            $this->info("Using user ID: {$userId} ({$user->name})");
        } else {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found.");
                return 1;
            }
        }

        $this->info("Form: {$form->form_name}");
        $this->info("Target User: {$form->target_user}");

        // Handle bulk data
        if ($this->option('bulk')) {
            return $this->handleBulkData($formId, $this->option('bulk'));
        }

        // Get form data
        $formData = $this->getFormData();
        if (!$formData) {
            return 1;
        }

        // Add the data
        try {
            $submissionId = FormSubmission::createSubmission($formId, $userId, $formData);
            
            if ($submissionId) {
                $this->info("✅ Form data added successfully!");
                $this->info("Submission ID: {$submissionId}");
                $this->info("Table: form_" . \Illuminate\Support\Str::slug($form->form_name, '_'));
            } else {
                $this->error("❌ Failed to create form submission.");
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            Log::error('Command form:add-data failed', [
                'form_id' => $formId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return 1;
        }

        return 0;
    }

    /**
     * Get form data from various sources
     */
    private function getFormData()
    {
        // Check if data provided via --data option
        if ($this->option('data')) {
            try {
                $data = json_decode($this->option('data'), true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error("Invalid JSON in --data option: " . json_last_error_msg());
                    return null;
                }
                return $data;
            } catch (\Exception $e) {
                $this->error("Error parsing --data option: " . $e->getMessage());
                return null;
            }
        }

        // Check if data provided via --file option
        if ($this->option('file')) {
            $filePath = $this->option('file');
            if (!file_exists($filePath)) {
                $this->error("File not found: {$filePath}");
                return null;
            }

            try {
                $content = file_get_contents($filePath);
                $data = json_decode($content, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error("Invalid JSON in file {$filePath}: " . json_last_error_msg());
                    return null;
                }
                return $data;
            } catch (\Exception $e) {
                $this->error("Error reading file {$filePath}: " . $e->getMessage());
                return null;
            }
        }

        // Interactive mode
        $this->info("No data provided. Entering interactive mode...");
        $data = [];

        while (true) {
            $field = $this->ask('Enter field name (or "done" to finish)');
            if (strtolower($field) === 'done') {
                break;
            }

            $value = $this->ask("Enter value for '{$field}'");
            $data[$field] = $value;
        }

        if (empty($data)) {
            $this->error("No form data provided.");
            return null;
        }

        return $data;
    }

    /**
     * Handle bulk data insertion
     */
    private function handleBulkData($formId, $filePath)
    {
        if (!file_exists($filePath)) {
            $this->error("Bulk file not found: {$filePath}");
            return 1;
        }

        try {
            $content = file_get_contents($filePath);
            $submissions = json_decode($content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error("Invalid JSON in bulk file: " . json_last_error_msg());
                return 1;
            }

            if (!is_array($submissions)) {
                $this->error("Bulk file must contain an array of submissions.");
                return 1;
            }

            $this->info("Processing " . count($submissions) . " submissions...");
            
            $successCount = 0;
            $errorCount = 0;

            foreach ($submissions as $index => $submission) {
                try {
                    $userId = $submission['user_id'] ?? User::first()->id;
                    $formData = $submission['form_data'] ?? [];

                    if (empty($formData)) {
                        $this->warn("Skipping submission {$index}: No form data");
                        $errorCount++;
                        continue;
                    }

                    $submissionId = FormSubmission::createSubmission($formId, $userId, $formData);
                    
                    if ($submissionId) {
                        $successCount++;
                        $this->info("✅ Submission {$index} added (ID: {$submissionId})");
                    } else {
                        $errorCount++;
                        $this->error("❌ Failed to add submission {$index}");
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    $this->error("❌ Error in submission {$index}: " . $e->getMessage());
                }
            }

            $this->info("\n📊 Bulk insertion completed:");
            $this->info("✅ Successful: {$successCount}");
            $this->info("❌ Failed: {$errorCount}");
            $this->info("📝 Total: " . count($submissions));

            return $errorCount > 0 ? 1 : 0;

        } catch (\Exception $e) {
            $this->error("Error processing bulk file: " . $e->getMessage());
            return 1;
        }
    }
}
