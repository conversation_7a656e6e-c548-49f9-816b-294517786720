/*! For license information please see form-builder.min.js.LICENSE.txt */
!function(e){"use strict";!function(){var t={349:function(e,t,r){r.r(t);var o=r(645),n=r.n(o),i=r(278),l=r.n(i),a=r(21),s=r.n(a),d=new URL(r(138),r.b),c=new URL(r(488),r.b),f=new URL(r(591),r.b),u=l()(n()),p=s()(d),m=s()(c),b=s()(f);u.push([e.id,'@font-face{font-family:"formbuilder-icons";src:url('+p+') format("woff")}[class^=formbuilder-icon-]:before,[class*=" formbuilder-icon-"]:before{font-family:"formbuilder-icons";font-style:normal;font-weight:normal;speak:never;display:inline-block;text-decoration:inherit;width:1em;margin-right:.2em;text-align:center;font-variant:normal;text-transform:none;line-height:1em;margin-left:.2em}.formbuilder-icon-autocomplete:before{content:""}.formbuilder-icon-date:before{content:""}.formbuilder-icon-checkbox:before{content:""}.formbuilder-icon-checkbox-group:before{content:""}.formbuilder-icon-radio-group:before{content:""}.formbuilder-icon-rich-text:before{content:""}.formbuilder-icon-select:before{content:""}.formbuilder-icon-textarea:before{content:""}.formbuilder-icon-text:before{content:""}.formbuilder-icon-pencil:before{content:""}.formbuilder-icon-file:before{content:""}.formbuilder-icon-hidden:before{content:""}.formbuilder-icon-cancel:before{content:""}.formbuilder-icon-button:before{content:""}.formbuilder-icon-header:before{content:""}.formbuilder-icon-paragraph:before{content:""}.formbuilder-icon-number:before{content:""}.formbuilder-icon-copy:before{content:""}.formbuilder-icon-grid:before{content:url('+m+")}.formbuilder-icon-plus:before{content:url("+b+')}.formbuilder-icon-sort-lower:before{content:""}.formbuilder-icon-sort-higher:before{content:""}.form-wrap.form-builder{position:relative;display:flex;flex-direction:row}.form-wrap.form-builder *{box-sizing:border-box}.form-wrap.form-builder.formbuilder-embedded-bootstrap button,.form-wrap.form-builder.formbuilder-embedded-bootstrap input,.form-wrap.form-builder.formbuilder-embedded-bootstrap select,.form-wrap.form-builder.formbuilder-embedded-bootstrap textarea{font-family:inherit;font-size:inherit;line-height:inherit}.form-wrap.form-builder.formbuilder-embedded-bootstrap input{line-height:normal}.form-wrap.form-builder.formbuilder-embedded-bootstrap textarea{overflow:auto}.form-wrap.form-builder.formbuilder-embedded-bootstrap button,.form-wrap.form-builder.formbuilder-embedded-bootstrap input,.form-wrap.form-builder.formbuilder-embedded-bootstrap select,.form-wrap.form-builder.formbuilder-embedded-bootstrap textarea{font-family:inherit;font-size:inherit;line-height:inherit}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group{position:relative;display:inline-block;vertical-align:middle}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.btn{position:relative;float:left}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle){border-radius:0}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .btn+.btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .btn+.btn-group,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .btn-group+.btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .btn-group+.btn-group{margin-left:-1px}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.btn:last-child:not(:first-child),.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.dropdown-toggle:not(:first-child),.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .input-group .form-control:last-child,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .input-group-addon:last-child,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .input-group-btn:first-child>.btn-group:not(:first-child)>.btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .input-group-btn:first-child>.btn:not(:first-child),.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .input-group-btn:last-child>.btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .input-group-btn:last-child>.btn-group>.btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group .input-group-btn:last-child>.dropdown-toggle{border-top-left-radius:0;border-bottom-left-radius:0}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.btn.active,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.btn:active,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.btn:focus,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn-group>.btn:hover{z-index:2}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn{display:inline-block;padding:6px 12px;margin-bottom:0;font-size:14px;font-weight:400;line-height:1.42857143;text-align:center;white-space:nowrap;vertical-align:middle;touch-action:manipulation;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none;background-image:none;border-radius:4px}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn.btn-lg{padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn.btn-sm{padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn.btn-xs{padding:1px 5px;font-size:12px;line-height:1.5;border-radius:3px}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn.active,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn.btn-active,.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn:active{background-image:none}.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group .form-control:last-child,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-addon:last-child,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-btn:first-child>.btn-group:not(:first-child)>.btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-btn:first-child>.btn:not(:first-child),.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-btn:last-child>.btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-btn:last-child>.btn-group>.btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-btn:last-child>.dropdown-toggle{border-top-left-radius:0;border-bottom-left-radius:0}.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group .form-control,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-addon,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-btn{display:table-cell}.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-lg>.form-control,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-lg>.input-group-addon,.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group-lg>.input-group-btn>.btn{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333}.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group{position:relative;display:table;border-collapse:separate}.form-wrap.form-builder.formbuilder-embedded-bootstrap .input-group .form-control{position:relative;z-index:2;float:left;width:100%;margin-bottom:0}.form-wrap.form-builder.formbuilder-embedded-bootstrap .form-control,.form-wrap.form-builder.formbuilder-embedded-bootstrap output{font-size:14px;line-height:1.42857143;display:block}.form-wrap.form-builder.formbuilder-embedded-bootstrap textarea.form-control{height:auto}.form-wrap.form-builder.formbuilder-embedded-bootstrap .form-control{height:34px;display:block;width:100%;padding:6px 12px;font-size:14px;line-height:1.42857143;border-radius:4px}.form-wrap.form-builder.formbuilder-embedded-bootstrap .form-control:focus{outline:0;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)}.form-wrap.form-builder.formbuilder-embedded-bootstrap .form-group{margin-left:0px;margin-bottom:15px}.form-wrap.form-builder.formbuilder-embedded-bootstrap .btn,.form-wrap.form-builder.formbuilder-embedded-bootstrap .form-control{background-image:none}.form-wrap.form-builder.formbuilder-embedded-bootstrap .pull-right{float:right}.form-wrap.form-builder.formbuilder-embedded-bootstrap .pull-left{float:left}.form-wrap.form-builder .formbuilder-required,.form-wrap.form-builder .required-asterisk{color:#c10000}.form-wrap.form-builder .formbuilder-checkbox-group input[type=checkbox],.form-wrap.form-builder .formbuilder-checkbox-group input[type=radio],.form-wrap.form-builder .formbuilder-radio-group input[type=checkbox],.form-wrap.form-builder .formbuilder-radio-group input[type=radio]{margin:0 4px 0 0}.form-wrap.form-builder .formbuilder-checkbox-inline,.form-wrap.form-builder .formbuilder-radio-inline{margin-right:8px;display:inline-block;vertical-align:middle;padding-left:0}.form-wrap.form-builder .formbuilder-checkbox-inline label input[type=text],.form-wrap.form-builder .formbuilder-radio-inline label input[type=text]{margin-top:0}.form-wrap.form-builder .formbuilder-checkbox-inline:first-child,.form-wrap.form-builder .formbuilder-radio-inline:first-child{padding-left:0}.form-wrap.form-builder .formbuilder-autocomplete-list{background-color:#fff;display:none;list-style:none;padding:0;border:1px solid #ccc;border-width:0 1px 1px;position:absolute;z-index:20;max-height:200px;overflow-y:auto}.form-wrap.form-builder .formbuilder-autocomplete-list li{display:none;cursor:default;padding:5px;margin:0;transition:background-color 200ms ease-in-out}.form-wrap.form-builder .formbuilder-autocomplete-list li:hover,.form-wrap.form-builder .formbuilder-autocomplete-list li.active-option{background-color:rgba(0,0,0,.075)}@keyframes PLACEHOLDER{0%{height:1px}100%{height:15px}}.form-wrap.form-builder .cb-wrap{width:26%;max-width:-moz-fit-content;max-width:fit-content;transition:transform 250ms}.form-wrap.form-builder .cb-wrap.sticky-controls{position:sticky;align-self:flex-start;top:0}.form-wrap.form-builder .cb-wrap h4{margin-top:0;color:#666}@media(max-width: 481px){.form-wrap.form-builder .cb-wrap{width:64px}.form-wrap.form-builder .cb-wrap h4{display:none}}.form-wrap.form-builder .cb-wrap .form-actions{float:right;margin-top:5px}.form-wrap.form-builder .cb-wrap .form-actions button{border:0 none}.form-wrap.form-builder .frmb-control{margin:0;padding:0;border-radius:5px}.form-wrap.form-builder .frmb-control li{cursor:move;list-style:none;margin:0 0 -1px 0;padding:10px;text-align:left;background:#fff;-webkit-user-select:none;-moz-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;box-shadow:inset 0 0 0 1px #c5c5c5}.form-wrap.form-builder .frmb-control li .control-icon{width:16px;height:auto;margin-right:10px;margin-left:.2em;display:inline-block}.form-wrap.form-builder .frmb-control li .control-icon img,.form-wrap.form-builder .frmb-control li .control-icon svg{max-width:100%;height:auto}.form-wrap.form-builder .frmb-control li:first-child{border-radius:5px 5px 0 0;margin-top:0}.form-wrap.form-builder .frmb-control li:last-child{border-radius:0 0 5px 5px}.form-wrap.form-builder .frmb-control li::before{margin-right:10px;font-size:16px}.form-wrap.form-builder .frmb-control li:hover{background-color:#f2f2f2}.form-wrap.form-builder .frmb-control li.ui-sortable-helper{border-radius:5px;transition:box-shadow 250ms;box-shadow:2px 2px 6px 0 #666;border:1px solid #fff}.form-wrap.form-builder .frmb-control li.ui-state-highlight{width:0;overflow:hidden;padding:0;margin:0;border:0 none}.form-wrap.form-builder .frmb-control li.moving{opacity:.6}.form-wrap.form-builder .frmb-control li.formbuilder-separator{background-color:rgba(0,0,0,0);box-shadow:none;padding:0;cursor:default}.form-wrap.form-builder .frmb-control li.formbuilder-separator hr{margin:10px 0}@media(max-width: 481px){.form-wrap.form-builder .frmb-control li::before{font-size:30px}.form-wrap.form-builder .frmb-control li{text-overflow:clip}.form-wrap.form-builder .frmb-control li span{visibility:hidden}.form-wrap.form-builder .frmb-control li span span{visibility:visible;font-size:30px;width:auto !important}}.form-wrap.form-builder .frmb-control.sort-enabled li.ui-state-highlight{box-shadow:none;height:0;width:100%;background:radial-gradient(ellipse at center, rgb(84, 84, 84) 0%, rgba(0, 0, 0, 0) 75%);border:0 none;clip-path:polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);visibility:visible;overflow:hidden;margin:1px 0 3px;animation:PLACEHOLDER 250ms forwards}.controls-left.form-wrap.form-builder .form-actions{float:left}.formbuilder-mobile.form-wrap.form-builder .form-actions{width:100%}.formbuilder-mobile.form-wrap.form-builder .form-actions button{width:100%;font-size:.85em !important;display:block !important;border-radius:0 !important;margin-top:-1px;margin-left:0 !important}.formbuilder-mobile.form-wrap.form-builder .form-actions button:first-child{border-radius:5px 5px 0 0 !important;margin-top:0 !important;border-bottom:0 none}.formbuilder-mobile.form-wrap.form-builder .form-actions button:last-child{border-radius:0 0 5px 5px !important}.form-wrap.form-builder .stage-wrap{flex-grow:1;display:flex;flex-direction:column;position:relative;padding:0;margin:0;width:calc(74% - 5px)}@media(max-width: 481px){.form-wrap.form-builder .stage-wrap{width:calc(100% - 64px)}}.form-wrap.form-builder .stage-wrap.empty{border:3px dashed #ccc;background-color:rgba(255,255,255,.25)}.form-wrap.form-builder .stage-wrap.empty::after{content:attr(data-content);position:absolute;text-align:center;top:50%;left:0;width:100%;margin-top:-1em}.form-wrap.form-builder .frmb{list-style-type:none;min-height:200px;transition:background-color 500ms ease-in-out}.form-wrap.form-builder .frmb .formbuilder-required{color:#c10000}.form-wrap.form-builder .frmb.removing{overflow:hidden}.form-wrap.form-builder .frmb li.form-field:hover{border-color:#66afe9;outline:0;box-shadow:inset 0 1px 1px rgba(0,0,0,.1),0 0 8px rgba(102,175,233,.6)}.form-wrap.form-builder .frmb li.form-field:hover .field-actions{opacity:1}.form-wrap.form-builder .frmb li.form-field:hover li :hover{background:#fefefe}.form-wrap.form-builder .frmb li.form-field{position:relative;padding:6px;clear:both;margin-left:0;margin-bottom:3px;background-color:#fff;transition:background-color 250ms ease-in-out,margin-top 400ms}.form-wrap.form-builder .frmb li.form-field.hidden-field{background-color:rgba(255,255,255,.6)}.form-wrap.form-builder .frmb li.form-field:first-child{border-top-right-radius:5px;border-top-left-radius:5px}.form-wrap.form-builder .frmb li.form-field:first-child .field-actions .btn:last-child{border-radius:0 5px 0 0}.form-wrap.form-builder .frmb li.form-field:last-child{border-bottom-right-radius:5px;border-bottom-left-radius:5px}.form-wrap.form-builder .frmb li.form-field.no-fields label{font-weight:400}@keyframes PLACEHOLDER{0%{height:0}100%{height:15px}}.form-wrap.form-builder .frmb li.form-field.frmb-placeholder,.form-wrap.form-builder .frmb li.form-field.ui-state-highlight{height:0;padding:0;background:radial-gradient(ellipse at center, rgb(84, 84, 84) 0%, rgba(0, 0, 0, 0) 75%);border:0 none;clip-path:polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);visibility:visible;overflow:hidden;margin-bottom:3px;animation:PLACEHOLDER 250ms forwards}.form-wrap.form-builder .frmb li.form-field.moving,.form-wrap.form-builder .frmb li.form-field.ui-sortable-helper{transition:box-shadow 500ms ease-in-out;box-shadow:2px 2px 6px 0 #666;border:1px solid #fff;border-radius:5px}.form-wrap.form-builder .frmb li.form-field.disabled-field{z-index:1;position:relative;overflow:visible}.form-wrap.form-builder .frmb li.form-field.disabled-field:hover .frmb-tt{display:inline-block}.form-wrap.form-builder .frmb li.form-field.disabled-field [type=checkbox]{float:left;margin-right:10px}.form-wrap.form-builder .frmb li.form-field.disabled-field h2{border-bottom:0 none}.form-wrap.form-builder .frmb li.form-field.disabled-field label{font-size:12px;font-weight:400;color:#666}.form-wrap.form-builder .frmb li.form-field.disabled-field .prev-holder{cursor:default;line-height:28px;padding-left:5px}.form-wrap.form-builder .frmb li.form-field .close-field{position:absolute;color:#666;left:50%;bottom:6px;background:#fff;border-top:1px solid #c5c5c5;border-left:1px solid #c5c5c5;border-right:1px solid #c5c5c5;transform:translateX(-50%);padding:0 5px;border-top-right-radius:3px;border-top-left-radius:3px;cursor:pointer;transition:background-color 250ms ease-in-out}.form-wrap.form-builder .frmb li.form-field .close-field:hover{text-decoration:none}.form-wrap.form-builder .frmb li.form-field.header-field h1,.form-wrap.form-builder .frmb li.form-field.header-field h2,.form-wrap.form-builder .frmb li.form-field.header-field h3,.form-wrap.form-builder .frmb li.form-field.header-field h4,.form-wrap.form-builder .frmb li.form-field.header-field h5,.form-wrap.form-builder .frmb li.form-field.header-field h6{word-break:break-word}.form-wrap.form-builder .frmb li.form-field.paragraph-field p{word-break:break-word}.form-wrap.form-builder .frmb li.form-field .field-label{display:block;overflow-wrap:break-word}.form-wrap.form-builder .frmb li.form-field.button-field h1,.form-wrap.form-builder .frmb li.form-field.button-field h2,.form-wrap.form-builder .frmb li.form-field.button-field h3,.form-wrap.form-builder .frmb li.form-field.button-field p,.form-wrap.form-builder .frmb li.form-field.button-field canvas,.form-wrap.form-builder .frmb li.form-field.button-field output,.form-wrap.form-builder .frmb li.form-field.button-field address,.form-wrap.form-builder .frmb li.form-field.button-field blockquote,.form-wrap.form-builder .frmb li.form-field.button-field .prev-holder,.form-wrap.form-builder .frmb li.form-field.header-field h1,.form-wrap.form-builder .frmb li.form-field.header-field h2,.form-wrap.form-builder .frmb li.form-field.header-field h3,.form-wrap.form-builder .frmb li.form-field.header-field p,.form-wrap.form-builder .frmb li.form-field.header-field canvas,.form-wrap.form-builder .frmb li.form-field.header-field output,.form-wrap.form-builder .frmb li.form-field.header-field address,.form-wrap.form-builder .frmb li.form-field.header-field blockquote,.form-wrap.form-builder .frmb li.form-field.header-field .prev-holder,.form-wrap.form-builder .frmb li.form-field.paragraph-field h1,.form-wrap.form-builder .frmb li.form-field.paragraph-field h2,.form-wrap.form-builder .frmb li.form-field.paragraph-field h3,.form-wrap.form-builder .frmb li.form-field.paragraph-field p,.form-wrap.form-builder .frmb li.form-field.paragraph-field canvas,.form-wrap.form-builder .frmb li.form-field.paragraph-field output,.form-wrap.form-builder .frmb li.form-field.paragraph-field address,.form-wrap.form-builder .frmb li.form-field.paragraph-field blockquote,.form-wrap.form-builder .frmb li.form-field.paragraph-field .prev-holder{margin:0}.form-wrap.form-builder .frmb li.form-field.button-field .field-label,.form-wrap.form-builder .frmb li.form-field.header-field .field-label,.form-wrap.form-builder .frmb li.form-field.paragraph-field .field-label{display:none}.form-wrap.form-builder .frmb li.form-field.button-field.editing .field-label,.form-wrap.form-builder .frmb li.form-field.header-field.editing .field-label,.form-wrap.form-builder .frmb li.form-field.paragraph-field.editing .field-label{display:block}.form-wrap.form-builder .frmb li.form-field.paragraph-field .fld-label{min-height:150px;overflow-y:auto}.form-wrap.form-builder .frmb li.form-field.checkbox-field .field-label{display:none}.form-wrap.form-builder .frmb li.deleting,.form-wrap.form-builder .frmb li.delete:hover,.form-wrap.form-builder .frmb li:hover li.delete:hover{background-color:#fdd}.form-wrap.form-builder .frmb li.deleting .close-field,.form-wrap.form-builder .frmb li.delete:hover .close-field,.form-wrap.form-builder .frmb li:hover li.delete:hover .close-field{background-color:#fdd}.form-wrap.form-builder .frmb li.deleting{z-index:20;pointer-events:none}.form-wrap.form-builder .frmb.disabled-field{padding:0 5px}.form-wrap.form-builder .frmb.disabled-field :hover{border-color:rgba(0,0,0,0)}.form-wrap.form-builder .frmb.disabled-field .form-element{float:none;margin-bottom:10px;overflow:visible;padding:5px 0;position:relative}.form-wrap.form-builder .frmb .frm-holder{display:none}.form-wrap.form-builder .frmb .tooltip{left:20px}.form-wrap.form-builder .frmb .prev-holder{display:block}.form-wrap.form-builder .frmb .prev-holder .form-group{margin:0}.form-wrap.form-builder .frmb .prev-holder .ql-editor{min-height:125px}.form-wrap.form-builder .frmb .prev-holder .form-group>label:not([class=formbuilder-checkbox-label]){display:none}.form-wrap.form-builder .frmb .prev-holder select,.form-wrap.form-builder .frmb .prev-holder input[type=text],.form-wrap.form-builder .frmb .prev-holder textarea,.form-wrap.form-builder .frmb .prev-holder input[type=number]{background-color:#fff;border:1px solid #ccc;box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.form-wrap.form-builder .frmb .prev-holder input[type=color]{width:60px;padding:2px;display:inline-block}.form-wrap.form-builder .frmb .prev-holder input[type=date]{width:auto}.form-wrap.form-builder .frmb .prev-holder select[multiple]{height:auto}.form-wrap.form-builder .frmb .prev-holder label{font-weight:normal}.form-wrap.form-builder .frmb .prev-holder input[type=number]{width:auto;max-width:100%}.form-wrap.form-builder .frmb .prev-holder input[type=color]{width:60px;padding:2px;display:inline-block}.form-wrap.form-builder .frmb .required-asterisk{display:none}.form-wrap.form-builder .frmb .field-label,.form-wrap.form-builder .frmb .legend{color:#666;margin-bottom:5px;line-height:27px;font-size:16px;font-weight:normal}.form-wrap.form-builder .frmb .disabled-field .field-label{display:block}.form-wrap.form-builder .frmb .other-option:checked+label input{display:inline-block}.form-wrap.form-builder .frmb .other-val{margin-left:5px;display:none}.form-wrap.form-builder .frmb .field-actions{position:absolute;top:0;right:0;opacity:0}.form-wrap.form-builder .frmb .field-actions a::before{margin:0}.form-wrap.form-builder .frmb .field-actions a:hover{text-decoration:none;color:#000}.form-wrap.form-builder .frmb .field-actions .btn{display:inline-block;width:32px;height:32px;padding:0 6px;border-radius:0;border-color:#c5c5c5;background-color:#fff;color:#c5c5c5;line-height:32px;font-size:16px;border-width:0 0 1px 1px}.form-wrap.form-builder .frmb .field-actions .btn:first-child{border-bottom-left-radius:5px}.form-wrap.form-builder .frmb .field-actions .toggle-form:hover{border-color:#ccc;background-color:#65aac6;color:#fff}.form-wrap.form-builder .frmb .field-actions .toggle-form::before{margin:0}.form-wrap.form-builder .frmb .field-actions .copy-button:hover{background-color:#6fc665;color:#fff}.form-wrap.form-builder .frmb .field-actions .del-button:hover{background-color:#c66865;color:#fff}.form-wrap.form-builder .frmb .option-actions{text-align:right;margin-top:10px;width:100%;margin-left:2%}.form-wrap.form-builder .frmb .option-actions button,.form-wrap.form-builder .frmb .option-actions a{background:#fff;padding:5px 10px;border:1px solid #c5c5c5;font-size:14px;border-radius:5px;cursor:default}.form-wrap.form-builder .frmb .sortable-options-wrap{width:81.33333333%;display:inline-block}.form-wrap.form-builder .frmb .sortable-options-wrap label{font-weight:normal}@media(max-width: 481px){.form-wrap.form-builder .frmb .sortable-options-wrap{display:block;width:100%}}.form-wrap.form-builder .frmb .radio-group-field .sortable-options li:nth-child(2) .remove{display:none}.form-wrap.form-builder .frmb .sortable-options{display:inline-block;width:100%;margin-left:2%;background:#c5c5c5;margin-bottom:0;border-radius:2px;list-style:none;padding:0}.form-wrap.form-builder .frmb .sortable-options>li{cursor:move;margin:1px;padding:6px;background-color:#fff}.form-wrap.form-builder .frmb .sortable-options>li:nth-child(1) .remove{display:none}.form-wrap.form-builder .frmb .sortable-options>li .remove{position:relative;opacity:1;float:right;right:14px;height:18px;width:18px;top:8px;font-size:12px;padding:0;color:#c10000}.form-wrap.form-builder .frmb .sortable-options>li .remove::before{margin:0}.form-wrap.form-builder .frmb .sortable-options>li .remove:hover{background-color:#c10000 !important;text-decoration:none;color:#fff}.form-wrap.form-builder .frmb .sortable-options .option-selected{margin:0;width:5%}.form-wrap.form-builder .frmb .sortable-options input[type=text]{width:calc(44.5% - 17px);margin:0 3px;float:none}.form-wrap.form-builder .frmb .form-field .form-group{width:100%;clear:left;float:none}.form-wrap.form-builder .frmb .col-md-6 .form-elements,.form-wrap.form-builder .frmb .col-md-8 .form-elements{width:100%}.form-wrap.form-builder .frmb .field-options .add-area .add{clear:both}.form-wrap.form-builder .frmb .style-wrap button.selected{border:1px solid #000;margin-top:0;margin-right:1px;box-shadow:0 0 0 1px #fff inset;padding:1px 5px}.form-wrap.form-builder .frmb .form-elements{padding:10px 5px;background:#f7f7f7;border-radius:3px;margin:0;border:1px solid #c5c5c5}.form-wrap.form-builder .frmb .form-elements .input-wrap{width:81.33333333%;margin-left:2%;float:left}.form-wrap.form-builder .frmb .form-elements .input-wrap>input[type=checkbox]{margin-top:8px}.form-wrap.form-builder .frmb .form-elements .btn-group{margin-left:2%}.form-wrap.form-builder .frmb .form-elements .add{clear:both}.form-wrap.form-builder .frmb .form-elements [contenteditable],.form-wrap.form-builder .frmb .form-elements select[multiple]{height:auto}.form-wrap.form-builder .frmb .form-elements [contenteditable].form-control{display:inline-block}.form-wrap.form-builder .frmb .form-elements [contenteditable].form-control,.form-wrap.form-builder .frmb .form-elements input[type=text],.form-wrap.form-builder .frmb .form-elements input[type=number],.form-wrap.form-builder .frmb .form-elements input[type=date],.form-wrap.form-builder .frmb .form-elements input[type=color],.form-wrap.form-builder .frmb .form-elements textarea,.form-wrap.form-builder .frmb .form-elements select{transition:background 250ms ease-in-out;padding:6px 12px;border:1px solid #c5c5c5;background-color:#fff}@media(max-width: 481px){.form-wrap.form-builder .frmb .form-elements .input-wrap{width:100%;margin-left:0;float:none}}.form-wrap.form-builder .frmb .form-elements input[type=number]{width:auto}.form-wrap.form-builder .frmb .form-elements .btn-group{margin-left:2%}.col-md-6 .form-wrap.form-builder .frmb .form-elements .false-label,.col-md-8 .form-wrap.form-builder .frmb .form-elements .false-label,.col-md-6 .form-wrap.form-builder .frmb .form-elements label,.col-md-8 .form-wrap.form-builder .frmb .form-elements label{display:block}.form-wrap.form-builder .frmb .form-elements .false-label:first-child,.form-wrap.form-builder .frmb .form-elements label:first-child{width:16.66666667%;padding-top:7px;margin-bottom:0;text-align:right;font-weight:700;float:left;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize}@media(max-width: 481px){.form-wrap.form-builder .frmb .form-elements .false-label:first-child,.form-wrap.form-builder .frmb .form-elements label:first-child{display:block;width:auto;float:none;text-align:left}.form-wrap.form-builder .frmb .form-elements .false-label:first-child.empty-label,.form-wrap.form-builder .frmb .form-elements label:first-child.empty-label{display:none}}.form-wrap.form-builder .frmb .form-elements .false-label.multiple,.form-wrap.form-builder .frmb .form-elements .false-label.required-label,.form-wrap.form-builder .frmb .form-elements .false-label.toggle-label,.form-wrap.form-builder .frmb .form-elements .false-label.roles-label,.form-wrap.form-builder .frmb .form-elements .false-label.other-label,.form-wrap.form-builder .frmb .form-elements label.multiple,.form-wrap.form-builder .frmb .form-elements label.required-label,.form-wrap.form-builder .frmb .form-elements label.toggle-label,.form-wrap.form-builder .frmb .form-elements label.roles-label,.form-wrap.form-builder .frmb .form-elements label.other-label{text-align:left;float:none;margin-bottom:-3px;font-weight:400;width:calc(81.3333% - 23px)}.form-wrap.form-builder .frmb .form-elements input.error{border:1px solid #c10000}.form-wrap.form-builder .frmb .form-elements input.fld-maxlength,.form-wrap.form-builder .frmb .form-elements input.fld-rows{width:75px}.form-wrap.form-builder .frmb .form-elements input.field-error{background:#fefefe;border:1px solid #c5c5c5}.form-wrap.form-builder .frmb .form-elements label em{display:block;font-weight:400;font-size:.75em}.form-wrap.form-builder .frmb .form-elements label.maxlength-label{line-height:1em}.form-wrap.form-builder .frmb .form-elements .available-roles{display:none;padding:10px;margin:10px 0;background:#e6e6e6;box-shadow:inset 0 0 2px 0 #b3b3b3}@media(max-width: 481px){.form-wrap.form-builder .frmb .form-elements .available-roles{margin-left:0}}.form-wrap.form-builder .frmb .form-elements .available-roles label{font-weight:400;width:auto;float:none;display:inline}.form-wrap.form-builder .frmb .form-elements .available-roles input{display:inline;top:auto}.form-wrap.form-builder .autocomplete-field .sortable-options .option-selected{display:none}.formbuilder-mobile.form-wrap.form-builder .field-actions{opacity:1}.form-wrap.form-builder .snackbar{visibility:hidden;min-width:250px;margin-left:-125px;background-color:#333;color:#fff;text-align:center;border-radius:2px;padding:16px;position:fixed;z-index:1;left:50%;bottom:30px}.form-wrap.form-builder .snackbar.show{visibility:visible;animation:fadein .5s,fadeout .5s 2.5s}@keyframes fadein{from{bottom:0;opacity:0}to{bottom:30px;opacity:1}}@keyframes fadeout{from{bottom:30px;opacity:1}to{bottom:0;opacity:0}}.form-wrap.form-builder .ui-state-highlight{border-radius:3px;border:1px dashed #0d99f2;background-color:#e5f5f8;width:12px}.form-wrap.form-builder .moveHighlight{border:1px dashed #0d99f2 !important;background-color:#e5f5f8 !important}.form-wrap.form-builder .currentGridModeFieldHighlight{background-color:#e5f5f8 !important}.form-wrap.form-builder .grid-mode-help{background-color:#fff;border-top-left-radius:5px;border-top-right-radius:5px}.form-wrap.form-builder .grid-mode-help-row1{white-space:nowrap;text-overflow:ellipsis;overflow:hidden;max-width:1px}.form-wrap.form-builder .grid-mode-help-row2{white-space:nowrap}.form-wrap.form-builder .colWithInsertButtons{padding-left:0 !important;padding-right:0 !important;flex:95 1 0% !important}.form-wrap.form-builder .rowWrapper{margin-left:0 !important;margin-right:0 !important}.form-wrap.form-builder .rowWrapper:last-child{flex-grow:1}.form-wrap.form-builder .rowWrapper:not(.tempRowWrapper){padding-top:1em;padding-bottom:1em}.form-wrap.form-builder .btnAddControl{border:0;background-color:unset}.form-wrap.form-builder .hoverColumnDropStyle{border:1px dashed #0d99f2;border-radius:3px;background-color:#e5f5f8;width:20px;position:fixed;margin-left:40px}.form-wrap.form-builder .hoverDropStyleInverse{background-color:#0d99f2;border:1px dashed #e5f5f8;min-height:20px}.form-wrap.form-builder .hoverDropStyleInverse .colWrapper{max-width:calc(100% - 40px)}.form-wrap.form-builder .stage-wrap>.hoverDropStyleInverse{width:100%}.form-wrap.form-builder .rowWrapper>.hoverDropStyleInverse{min-width:40px;flex-grow:1}.form-wrap.form-builder .hoverDropStyleInverse:last-child{flex-grow:1}.form-wrap.form-builder .invisibleRowPlaceholder{width:0 !important;position:fixed !important;left:-100px !important}.form-wrap.form-builder *[tooltip]{position:relative}.form-wrap.form-builder *[tooltip]:hover::after{background:rgba(0,0,0,.9);border-radius:5px 5px 5px 0;bottom:23px;color:#fff;content:attr(tooltip);padding:10px 5px;position:absolute;z-index:98;left:2px;width:230px;text-shadow:none;font-size:12px;line-height:1.5em;cursor:default}.form-wrap.form-builder *[tooltip]:hover::before{border:solid;border-color:#222 rgba(0,0,0,0);border-width:6px 6px 0;bottom:17px;content:"";left:2px;position:absolute;z-index:99;cursor:default}.form-wrap.form-builder .tooltip-element{visibility:visible;color:#fff;background:#000;width:16px;height:16px;border-radius:8px;display:inline-block;text-align:center;line-height:16px;margin:0 5px;font-size:12px;cursor:default}.form-wrap.form-builder .kc-toggle{padding-left:0 !important}.form-wrap.form-builder .kc-toggle span{position:relative;width:48px;height:24px;background:#e6e6e6;display:inline-block;border-radius:4px;border:1px solid #ccc;padding:2px;overflow:hidden;float:left;margin-right:5px;will-change:transform}.form-wrap.form-builder .kc-toggle span::after,.form-wrap.form-builder .kc-toggle span::before{position:absolute;display:inline-block;top:0}.form-wrap.form-builder .kc-toggle span::after{position:relative;content:"";width:50%;height:100%;left:0;border-radius:3px;background:linear-gradient(to bottom, white 0%, #ccc 100%);border:1px solid #999;transition:transform 100ms;transform:translateX(0)}.form-wrap.form-builder .kc-toggle span::before{border-radius:4px;top:2px;left:2px;content:"";width:calc(100% - 4px);height:18px;box-shadow:0 0 1px 1px #b3b3b3 inset;background-color:rgba(0,0,0,0)}.form-wrap.form-builder .kc-toggle input{height:0;overflow:hidden;width:0;opacity:0;pointer-events:none;margin:0}.form-wrap.form-builder .kc-toggle input:checked+span::after{transform:translateX(100%)}.form-wrap.form-builder .kc-toggle input:checked+span::before{background-color:#6fc665}.form-wrap.form-builder.controls-left{flex-direction:row-reverse}.form-wrap.form-builder::after{content:"";display:table;clear:both}.form-wrap.form-builder .cb-wrap,.form-wrap.form-builder .stage-wrap{vertical-align:top}.form-wrap.form-builder .form-elements,.form-wrap.form-builder .form-group,.form-wrap.form-builder .multi-row span,.form-wrap.form-builder textarea{display:block}.form-wrap.form-builder .form-elements::after,.form-wrap.form-builder .form-group::after{content:".";display:block;height:0;clear:both;visibility:hidden}.form-wrap.form-builder .form-elements .field-options div:hover,.form-wrap.form-builder .frmb .legend,.form-wrap.form-builder .frmb .prev-holder{cursor:move}.form-wrap.form-builder .frmb-tt{display:none;position:absolute;top:0;left:0;border:1px solid #262626;background-color:#666;border-radius:5px;padding:5px;color:#fff;z-index:20;text-align:left;font-size:12px;pointer-events:none}.form-wrap.form-builder .frmb-tt::before{border-color:#262626 rgba(0,0,0,0);bottom:-11px}.form-wrap.form-builder .frmb-tt::before,.form-wrap.form-builder .frmb-tt::after{content:"";position:absolute;border-style:solid;border-width:10px 10px 0;border-color:#666 rgba(0,0,0,0);display:block;width:0;z-index:1;margin-left:-10px;bottom:-10px;left:20px}.form-wrap.form-builder .frmb-tt a{text-decoration:underline;color:#fff}.form-builder-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.5);display:none;z-index:10}.form-builder-overlay.visible{display:block}.form-builder-dialog{position:absolute;border-radius:5px;background:#fff;z-index:20;transform:translate(-50%, -50%);top:0;left:0;padding:10px;box-shadow:0 3px 10px #000;min-width:166px;max-height:80%;overflow-y:scroll}.form-builder-dialog h3{margin-top:0}.form-builder-dialog.data-dialog{width:65%;background-color:#23241f}.form-builder-dialog.data-dialog pre{background:none;border:0 none;box-shadow:none;margin:0;color:#f2f2f2}.form-builder-dialog.positioned{transform:translate(-50%, -100%)}.form-builder-dialog.positioned .button-wrap::before{content:"";width:0;height:0;border-left:15px solid rgba(0,0,0,0);border-right:15px solid rgba(0,0,0,0);border-top:10px solid #fff;position:absolute;left:50%;top:100%;transform:translate(-50%, 10px)}.form-builder-dialog .button-wrap{position:relative;margin-top:10px;text-align:right;clear:both}.form-builder-dialog .button-wrap .btn{margin-left:10px}',""]),t.default=u},278:function(e){e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",o=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),o&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),o&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,o,n,i){"string"==typeof e&&(e=[[null,e,void 0]]);var l={};if(o)for(var a=0;a<this.length;a++){var s=this[a][0];null!=s&&(l[s]=!0)}for(var d=0;d<e.length;d++){var c=[].concat(e[d]);o&&l[c[0]]||(void 0!==i&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=i),r&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=r):c[2]=r),n&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=n):c[4]="".concat(n)),t.push(c))}},t}},21:function(e){e.exports=function(e,t){return t||(t={}),e?(e=String(e.__esModule?e.default:e),/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]|(%20)/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e):e}},645:function(e){e.exports=function(e){return e[1]}},781:function(e,t,r){var o=r(433).Symbol;e.exports=o},148:function(e,t,r){var o=r(781),n=r(903),i=r(738),l=o?o.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":l&&l in Object(e)?n(e):i(e)}},812:function(e,t,r){var o=r(140),n=/^\s+/;e.exports=function(e){return e?e.slice(0,o(e)+1).replace(n,""):e}},380:function(e){var t="object"==typeof global&&global&&global.Object===Object&&global;e.exports=t},903:function(e,t,r){var o=r(781),n=Object.prototype,i=n.hasOwnProperty,l=n.toString,a=o?o.toStringTag:void 0;e.exports=function(e){var t=i.call(e,a),r=e[a];try{e[a]=void 0;var o=!0}catch(e){}var n=l.call(e);return o&&(t?e[a]=r:delete e[a]),n}},738:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},433:function(e,t,r){var o=r(380),n="object"==typeof self&&self&&self.Object===Object&&self,i=o||n||Function("return this")();e.exports=i},140:function(e){var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},177:function(e,t,r){var o=r(953),n=r(664),i=r(378),l=Math.max,a=Math.min;e.exports=function(e,t,r){var s,d,c,f,u,p,m=0,b=!1,h=!1,g=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function w(t){var r=s,o=d;return s=d=void 0,m=t,f=e.apply(o,r)}function y(e){var r=e-p;return void 0===p||r>=t||r<0||h&&e-m>=c}function v(){var e=n();if(y(e))return x(e);u=setTimeout(v,function(e){var r=t-(e-p);return h?a(r,c-(e-m)):r}(e))}function x(e){return u=void 0,g&&s?w(e):(s=d=void 0,f)}function A(){var e=n(),r=y(e);if(s=arguments,d=this,p=e,r){if(void 0===u)return function(e){return m=e,u=setTimeout(v,t),b?w(e):f}(p);if(h)return clearTimeout(u),u=setTimeout(v,t),w(p)}return void 0===u&&(u=setTimeout(v,t)),f}return t=i(t)||0,o(r)&&(b=!!r.leading,c=(h="maxWait"in r)?l(i(r.maxWait)||0,t):c,g="trailing"in r?!!r.trailing:g),A.cancel=function(){void 0!==u&&clearTimeout(u),m=0,s=p=d=u=void 0},A.flush=function(){return void 0===u?f:x(n())},A}},953:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},934:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},414:function(e,t,r){var o=r(148),n=r(934);e.exports=function(e){return"symbol"==typeof e||n(e)&&"[object Symbol]"==o(e)}},664:function(e,t,r){var o=r(433);e.exports=function(){return o.Date.now()}},858:function(e,t,r){var o=r(177),n=r(953);e.exports=function(e,t,r){var i=!0,l=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return n(r)&&(i="leading"in r?!!r.leading:i,l="trailing"in r?!!r.trailing:l),o(e,t,{leading:i,maxWait:t,trailing:l})}},378:function(e,t,r){var o=r(812),n=r(953),i=r(414),l=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,s=/^0o[0-7]+$/i,d=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return NaN;if(n(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=n(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=o(e);var r=a.test(e);return r||s.test(e)?d(e.slice(2),r?2:8):l.test(e)?NaN:+e}},252:function(e){e.exports=function(e){var t={};function r(o){if(t[o])return t[o].exports;var n=t[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,r),n.l=!0,n.exports}return r.m=e,r.c=t,r.d=function(e,t,o){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(o,n,function(t){return e[t]}.bind(null,n));return o},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=7)}([function(e,t,r){var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=r(2),i=r(10),l=Object.prototype.toString;function a(e){return"[object Array]"===l.call(e)}function s(e){return null!==e&&"object"===(void 0===e?"undefined":o(e))}function d(e){return"[object Function]"===l.call(e)}function c(e,t){if(null!=e)if("object"!==(void 0===e?"undefined":o(e))&&(e=[e]),a(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:a,isArrayBuffer:function(e){return"[object ArrayBuffer]"===l.call(e)},isBuffer:i,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===l.call(e)},isFile:function(e){return"[object File]"===l.call(e)},isBlob:function(e){return"[object Blob]"===l.call(e)},isFunction:d,isStream:function(e){return s(e)&&d(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:c,merge:function e(){var t={};function r(r,n){"object"===o(t[n])&&"object"===(void 0===r?"undefined":o(r))?t[n]=e(t[n],r):t[n]=r}for(var n=0,i=arguments.length;n<i;n++)c(arguments[n],r);return t},extend:function(e,t,r){return c(t,(function(t,o){e[o]=r&&"function"==typeof t?n(t,r):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},function(e,t,r){(function(t){var o=r(0),n=r(13),i={"Content-Type":"application/x-www-form-urlencoded"};function l(e,t){!o.isUndefined(e)&&o.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a={adapter:function(){var e;return("undefined"!=typeof XMLHttpRequest||void 0!==t)&&(e=r(3)),e}(),transformRequest:[function(e,t){return n(t,"Content-Type"),o.isFormData(e)||o.isArrayBuffer(e)||o.isBuffer(e)||o.isStream(e)||o.isFile(e)||o.isBlob(e)?e:o.isArrayBufferView(e)?e.buffer:o.isURLSearchParams(e)?(l(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):o.isObject(e)?(l(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};o.forEach(["delete","get","head"],(function(e){a.headers[e]={}})),o.forEach(["post","put","patch"],(function(e){a.headers[e]=o.merge(i)})),e.exports=a}).call(this,r(12))},function(e,t,r){e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),o=0;o<r.length;o++)r[o]=arguments[o];return e.apply(t,r)}}},function(e,t,r){var o=r(0),n=r(14),i=r(16),l=r(17),a=r(18),s=r(4),d="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||r(19);e.exports=function(e){return new Promise((function(t,c){var f=e.data,u=e.headers;o.isFormData(f)&&delete u["Content-Type"];var p=new XMLHttpRequest,m="onreadystatechange",b=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in p||a(e.url)||(p=new window.XDomainRequest,m="onload",b=!0,p.onprogress=function(){},p.ontimeout=function(){}),e.auth){var h=e.auth.username||"",g=e.auth.password||"";u.Authorization="Basic "+d(h+":"+g)}if(p.open(e.method.toUpperCase(),i(e.url,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,p[m]=function(){if(p&&(4===p.readyState||b)&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in p?l(p.getAllResponseHeaders()):null,o={data:e.responseType&&"text"!==e.responseType?p.response:p.responseText,status:1223===p.status?204:p.status,statusText:1223===p.status?"No Content":p.statusText,headers:r,config:e,request:p};n(t,c,o),p=null}},p.onerror=function(){c(s("Network Error",e,null,p)),p=null},p.ontimeout=function(){c(s("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",p)),p=null},o.isStandardBrowserEnv()){var w=r(20),y=(e.withCredentials||a(e.url))&&e.xsrfCookieName?w.read(e.xsrfCookieName):void 0;y&&(u[e.xsrfHeaderName]=y)}if("setRequestHeader"in p&&o.forEach(u,(function(e,t){void 0===f&&"content-type"===t.toLowerCase()?delete u[t]:p.setRequestHeader(t,e)})),e.withCredentials&&(p.withCredentials=!0),e.responseType)try{p.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){p&&(p.abort(),c(e),p=null)})),void 0===f&&(f=null),p.send(f)}))}},function(e,t,r){var o=r(15);e.exports=function(e,t,r,n,i){var l=new Error(e);return o(l,t,r,n,i)}},function(e,t,r){e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,r){function o(e){this.message=e}o.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},o.prototype.__CANCEL__=!0,e.exports=o},function(e,t,r){t.__esModule=!0,t.I18N=void 0;var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),i=r(8),l={extension:".lang",location:"assets/lang/",langs:["en-US"],locale:"en-US",override:{}},a=t.I18N=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.langs=Object.create(null),this.loaded=[],this.processConfig(t)}return e.prototype.processConfig=function(e){var t=this,r=Object.assign({},l,e),o=r.location,n=function(e,t){var r={};for(var o in e)t.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o]);return r}(r,["location"]),i=o.replace(/\/?$/,"/");this.config=Object.assign({},{location:i},n);var a=this.config,s=a.override,d=a.preloaded,c=void 0===d?{}:d,f=Object.entries(this.langs).concat(Object.entries(s||c));this.langs=f.reduce((function(e,r){var o=r[0],n=r[1];return e[o]=t.applyLanguage.call(t,o,n),e}),{}),this.locale=this.config.locale||this.config.langs[0]},e.prototype.init=function(e){return this.processConfig.call(this,Object.assign({},this.config,e)),this.setCurrent(this.locale)},e.prototype.addLanguage=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t="string"==typeof t?this.processFile.call(this,t):t,this.applyLanguage.call(this,e,t),this.config.langs.push("locale")},e.prototype.getValue=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.locale;return this.langs[t]&&this.langs[t][e]||this.getFallbackValue(e)},e.prototype.getFallbackValue=function(e){var t=Object.values(this.langs).find((function(t){return t[e]}));return t&&t[e]},e.prototype.makeSafe=function(e){var t={"{":"\\{","}":"\\}","|":"\\|"};return e=e.replace(/\{|\}|\|/g,(function(e){return t[e]})),new RegExp(e,"g")},e.prototype.put=function(e,t){return this.current[e]=t},e.prototype.get=function(e,t){var r=this.getValue(e);if(r){var n=r.match(/\{[^}]+?\}/g),i=void 0;if(t&&n)if("object"===(void 0===t?"undefined":o(t)))for(var l=0;l<n.length;l++)i=n[l].substring(1,n[l].length-1),r=r.replace(this.makeSafe(n[l]),t[i]||"");else r=r.replace(/\{[^}]+?\}/g,t);return r}},e.prototype.fromFile=function(e){for(var t,r=e.split("\n"),o={},n=0;n<r.length;n++)(t=r[n].match(/^(.+?) *?= *?([^\n]+)/))&&(o[t[1]]=t[2].replace(/^\s+|\s+$/,""));return o},e.prototype.processFile=function(e){return this.fromFile(e.replace(/\n\n/g,"\n"))},e.prototype.loadLang=function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=this;return new Promise((function(o,n){if(-1!==r.loaded.indexOf(e)&&t)return r.applyLanguage.call(r,r.langs[e]),o(r.langs[e]);var l=[r.config.location,e,r.config.extension].join("");return(0,i.get)(l).then((function(t){var n=t.data,i=r.processFile(n);return r.applyLanguage.call(r,e,i),r.loaded.push(e),o(r.langs[e])})).catch((function(){var t=r.applyLanguage.call(r,e);o(t)}))}))},e.prototype.applyLanguage=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.config.override[e]||{},o=this.langs[e]||{};return this.langs[e]=Object.assign({},o,t,r),this.langs[e]},e.prototype.setCurrent=function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en-US";return this.loadLang(t).then((function(){return e.locale=t,e.current=e.langs[t],e.current}))},n(e,[{key:"getLangs",get:function(){return this.config.langs}}]),e}();t.default=new a},function(e,t,r){e.exports=r(9)},function(e,t,r){var o=r(0),n=r(2),i=r(11),l=r(1);function a(e){var t=new i(e),r=n(i.prototype.request,t);return o.extend(r,i.prototype,t),o.extend(r,t),r}var s=a(l);s.Axios=i,s.create=function(e){return a(o.merge(l,e))},s.Cancel=r(6),s.CancelToken=r(26),s.isCancel=r(5),s.all=function(e){return Promise.all(e)},s.spread=r(27),e.exports=s,e.exports.default=s},function(e,t,r){function o(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(o(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&o(e.slice(0,0))}(e)||!!e._isBuffer)}},function(e,t,r){var o=r(1),n=r(0),i=r(21),l=r(22);function a(e){this.defaults=e,this.interceptors={request:new i,response:new i}}a.prototype.request=function(e){"string"==typeof e&&(e=n.merge({url:arguments[0]},arguments[1])),(e=n.merge(o,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var t=[l,void 0],r=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)r=r.then(t.shift(),t.shift());return r},n.forEach(["delete","get","head","options"],(function(e){a.prototype[e]=function(t,r){return this.request(n.merge(r||{},{method:e,url:t}))}})),n.forEach(["post","put","patch"],(function(e){a.prototype[e]=function(t,r,o){return this.request(n.merge(o||{},{method:e,url:t,data:r}))}})),e.exports=a},function(e,t,r){var o,n,i=e.exports={};function l(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(o===setTimeout)return setTimeout(e,0);if((o===l||!o)&&setTimeout)return o=setTimeout,setTimeout(e,0);try{return o(e,0)}catch(t){try{return o.call(null,e,0)}catch(t){return o.call(this,e,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:l}catch(e){o=l}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var d,c=[],f=!1,u=-1;function p(){f&&d&&(f=!1,d.length?c=d.concat(c):u=-1,c.length&&m())}function m(){if(!f){var e=s(p);f=!0;for(var t=c.length;t;){for(d=c,c=[];++u<t;)d&&d[u].run();u=-1,t=c.length}d=null,f=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function b(e,t){this.fun=e,this.array=t}function h(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new b(e,t)),1!==c.length||f||s(m)},b.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,r){var o=r(0);e.exports=function(e,t){o.forEach(e,(function(r,o){o!==t&&o.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[o])}))}},function(e,t,r){var o=r(4);e.exports=function(e,t,r){var n=r.config.validateStatus;r.status&&n&&!n(r.status)?t(o("Request failed with status code "+r.status,r.config,null,r.request,r)):e(r)}},function(e,t,r){e.exports=function(e,t,r,o,n){return e.config=t,r&&(e.code=r),e.request=o,e.response=n,e}},function(e,t,r){var o=r(0);function n(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,r){if(!t)return e;var i;if(r)i=r(t);else if(o.isURLSearchParams(t))i=t.toString();else{var l=[];o.forEach(t,(function(e,t){null!=e&&(o.isArray(e)?t+="[]":e=[e],o.forEach(e,(function(e){o.isDate(e)?e=e.toISOString():o.isObject(e)&&(e=JSON.stringify(e)),l.push(n(t)+"="+n(e))})))})),i=l.join("&")}return i&&(e+=(-1===e.indexOf("?")?"?":"&")+i),e}},function(e,t,r){var o=r(0),n=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,r,i,l={};return e?(o.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=o.trim(e.substr(0,i)).toLowerCase(),r=o.trim(e.substr(i+1)),t){if(l[t]&&n.indexOf(t)>=0)return;l[t]="set-cookie"===t?(l[t]?l[t]:[]).concat([r]):l[t]?l[t]+", "+r:r}})),l):l}},function(e,t,r){var o=r(0);e.exports=o.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function n(e){var o=e;return t&&(r.setAttribute("href",o),o=r.href),r.setAttribute("href",o),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return e=n(window.location.href),function(t){var r=o.isString(t)?n(t):t;return r.protocol===e.protocol&&r.host===e.host}}():function(){return!0}},function(e,t,r){function o(){this.message="String contains an invalid character"}o.prototype=new Error,o.prototype.code=5,o.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,r,n=String(e),i="",l=0,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";n.charAt(0|l)||(a="=",l%1);i+=a.charAt(63&t>>8-l%1*8)){if((r=n.charCodeAt(l+=.75))>255)throw new o;t=t<<8|r}return i}},function(e,t,r){var o=r(0);e.exports=o.isStandardBrowserEnv()?{write:function(e,t,r,n,i,l){var a=[];a.push(e+"="+encodeURIComponent(t)),o.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),o.isString(n)&&a.push("path="+n),o.isString(i)&&a.push("domain="+i),!0===l&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t,r){var o=r(0);function n(){this.handlers=[]}n.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},n.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},n.prototype.forEach=function(e){o.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=n},function(e,t,r){var o=r(0),n=r(23),i=r(5),l=r(1),a=r(24),s=r(25);function d(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return d(e),e.baseURL&&!a(e.url)&&(e.url=s(e.baseURL,e.url)),e.headers=e.headers||{},e.data=n(e.data,e.headers,e.transformRequest),e.headers=o.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),o.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||l.adapter)(e).then((function(t){return d(e),t.data=n(t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(d(e),t&&t.response&&(t.response.data=n(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},function(e,t,r){var o=r(0);e.exports=function(e,t,r){return o.forEach(r,(function(r){e=r(e,t)})),e}},function(e,t,r){e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},function(e,t,r){e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,r){var o=r(6);function n(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var r=this;e((function(e){r.reason||(r.reason=new o(e),t(r.reason))}))}n.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},n.source=function(){var e;return{token:new n((function(t){e=t})),cancel:e}},e.exports=n},function(e,t,r){e.exports=function(e){return function(t){return e.apply(null,t)}}}])},395:function(e){e.exports=function(e){try{var t=window[e],r="__storage_test__";return t.setItem(r,r),t.removeItem(r),!0}catch(e){return!1}}},287:function(e,t,r){var o=r(292),n=r(349);"string"==typeof(n=n.__esModule?n.default:n)&&(n=[[e.id,n,""]]);o(n,{attributes:{class:"formBuilder-injected-style"},insert:"head",singleton:!1}),e.exports=n.locals||{}},292:function(e,t,r){var o,n=function(){var e={};return function(t){if(void 0===e[t]){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}e[t]=r}return e[t]}}(),i=[];function l(e){for(var t=-1,r=0;r<i.length;r++)if(i[r].identifier===e){t=r;break}return t}function a(e,t){for(var r={},o=[],n=0;n<e.length;n++){var a=e[n],s=t.base?a[0]+t.base:a[0],d=r[s]||0,c="".concat(s," ").concat(d);r[s]=d+1;var f=l(c),u={css:a[1],media:a[2],sourceMap:a[3]};-1!==f?(i[f].references++,i[f].updater(u)):i.push({identifier:c,updater:b(u,t),references:1}),o.push(c)}return o}function s(e){var t=document.createElement("style"),o=e.attributes||{};if(void 0===o.nonce){var i=r.nc;i&&(o.nonce=i)}if(Object.keys(o).forEach((function(e){t.setAttribute(e,o[e])})),"function"==typeof e.insert)e.insert(t);else{var l=n(e.insert||"head");if(!l)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");l.appendChild(t)}return t}var d,c=(d=[],function(e,t){return d[e]=t,d.filter(Boolean).join("\n")});function f(e,t,r,o){var n=r?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(e.styleSheet)e.styleSheet.cssText=c(t,n);else{var i=document.createTextNode(n),l=e.childNodes;l[t]&&e.removeChild(l[t]),l.length?e.insertBefore(i,l[t]):e.appendChild(i)}}function u(e,t,r){var o=r.css,n=r.media,i=r.sourceMap;if(n?e.setAttribute("media",n):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=o;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(o))}}var p=null,m=0;function b(e,t){var r,o,n;if(t.singleton){var i=m++;r=p||(p=s(t)),o=f.bind(null,r,i,!1),n=f.bind(null,r,i,!0)}else r=s(t),o=u.bind(null,r,t),n=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(r)};return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else n()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=(void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o));var r=a(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var o=0;o<r.length;o++){var n=l(r[o]);i[n].references--}for(var s=a(e,t),d=0;d<r.length;d++){var c=l(r[d]);0===i[c].references&&(i[c].updater(),i.splice(c,1))}r=s}}}},138:function(e){e.exports="data:font/woff;base64,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"},488:function(e){e.exports="data:image/svg+xml; utf8, <svg xmlns=%27http://www.w3.org/2000/svg%27 width=%2716%27 height=%2716%27 fill=%27currentColor%27 class=%27bi bi-list-nested%27 viewBox=%270 0 16 16%27><path fill-rule=%27evenodd%27 d=%27M4.5 11.5A.5.5 0 0 1 5 11h10a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 3 7h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 1 3h10a.5.5 0 0 1 0 1H1a.5.5 0 0 1-.5-.5z%27/></svg>"},591:function(e){e.exports="data:image/svg+xml; utf8,<svg xmlns=%27http://www.w3.org/2000/svg%27 width=%2716%27 height=%2716%27 fill=%27currentColor%27 class=%27bi bi-plus-circle%27 viewBox=%270 0 16 16%27><path d=%27M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z%27/><path d=%27M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z%27/></svg>"}},r={};function o(e){var n=r[e];if(void 0!==n)return n.exports;var i=r[e]={id:e,exports:{}};return t[e](i,i.exports,o),i.exports}o.m=t,o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.b=document.baseURI||self.location.href,o.nc=void 0,function(){o(287);var t=o(858),r=o.n(t);function n(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,"string");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const i={},l={text:["text","password","email","color","tel"],header:["h1","h2","h3"],button:["button","submit","reset"],paragraph:["p","address","blockquote","canvas","output"],textarea:["textarea","quill"]},a=e=>{e.parentNode&&e.parentNode.removeChild(e)},s=e=>{for(;e.firstChild;)e.removeChild(e.firstChild);return e},d=(e,t,r=!0)=>{const o=[];let n=["none","block"];r&&(n=n.reverse());for(let r=e.length-1;r>=0;r--)-1!==e[r].textContent.toLowerCase().indexOf(t.toLowerCase())?(e[r].style.display=n[0],o.push(e[r])):e[r].style.display=n[1];return o},c=["select","checkbox-group","checkbox","radio-group","autocomplete"],f=new RegExp(`(${c.join("|")})`);class u{constructor(e){return n(this,"stage",void 0),n(this,"controls",void 0),n(this,"formActions",void 0),n(this,"editorWrap",void 0),this.optionFields=c,this.optionFieldsRegEx=f,this.subtypes=l,this.empty=s,this.filter=d,i[e]=this,i[e]}onRender(e,t){e.parentElement?t(e):window.requestAnimationFrame((()=>this.onRender(e,t)))}}const p={};class m{constructor(e){this.formData={},this.formID=e,p[e]=this}}var b=o(252),h=o.n(b);function g(e){let t;return"function"==typeof Event?t=new Event(e):(t=document.createEvent("Event"),t.initEvent(e,!0,!0)),t}var w={loaded:g("loaded"),viewData:g("viewData"),userDeclined:g("userDeclined"),modalClosed:g("modalClosed"),modalOpened:g("modalOpened"),formSaved:g("formSaved"),fieldAdded:g("fieldAdded"),fieldRemoved:g("fieldRemoved"),fieldRendered:g("fieldRendered"),fieldEditOpened:g("fieldEditOpened"),fieldEditClosed:g("fieldEditClosed"),stageEmptied:g("stageEmptied")};const y={clobberingProtection:{document:!0,form:!0,namespaceAttributes:!1},backendOrder:["dompurify","sanitizer","fallback"],backends:{sanitizer:"function"==typeof window.Sanitizer&&new window.Sanitizer,dompurify:!!window.DOMPurify&&(v=window.DOMPurify,v.setConfig({SANITIZE_DOM:!1,ADD_ATTR:["contenteditable"]}),v),fallback:e=>e}};var v;const x=(e,t)=>{if(0===y.backendOrder.length)return!1;const r=e.toLowerCase();return t=t?t+"":"",r.startsWith("on")||["form","formaction"].includes(r)||t.trim().toLowerCase().startsWith("javascript:")};y.backends.fallback=function(t){const r=document.implementation.createHTMLDocument(""),o=r.createElement("base");o.href=document.location.href,r.head.appendChild(o);const n=["applet","comment","embed","iframe","link","listing","meta","noscript","object","plaintext","script","style","xmp"],i=e.parseHTML(t,r,!1);e(i).find("*").addBack().each(((t,r)=>{"#text"!==r.nodeName&&(r.tagName&&n.includes(r.tagName.toLowerCase())?r.parentElement?r.parentElement.removeChild(r):i.includes(r)&&i.splice(i.indexOf(r),1):r.attributes&&Array.from(r.attributes).forEach((t=>{x(t.name,t.value)&&e(r).removeAttr(t.name)})))}));const l=r.createElement("div");return e(l).html(i),l.innerHTML};const A=e=>{const t=!!y.clobberingProtection.document&&document,r=!!y.clobberingProtection.form&&document.createElement("form");return t&&e in t||r&&e in r?y.clobberingProtection.namespaceAttributes?"user-content-"+e:void 0:e},C={fallback:(e,t)=>{const r=y.backends.fallback,o="function"==typeof r;return o&&(t=r(t)),e.innerHTML=t,o},dompurify:(e,t)=>{const r=y.backends.dompurify;return!(!1===r||!r.isSupported||(e.innerHTML=r.sanitize(t),0))},sanitizer:(e,t)=>{const r=y.backends.sanitizer;return!!r&&(e.setHTML(t,{sanitizer:r}),!0)}},O=(t,r,o=!1)=>{if(!o){const o=document.createElement(t.tagName);return void 0!==y.backendOrder.find((e=>C[e](o,r)))?((t=>{e(t).find("*").each(((e,t)=>{["embed","form","iframe","image","img","object"].includes(t.tagName.toLowerCase())&&t.removeAttribute("name"),["id","name"].forEach((e=>{if(t.hasAttribute(e)){const r=A(t.getAttribute(e));void 0===r?t.removeAttribute(e):t.setAttribute(e,r)}}))}))})(o),t.innerHTML=o.innerHTML,t):(t.innerHTML=r,t)}t.textContent=r},k=["events"],E=["tag","content"];function j(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?j(Object(r),!0).forEach((function(t){N(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function N(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,"string");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}window.fbLoaded={js:[],css:[]},window.fbEditors={quill:{},tinymce:{}};const D=function(e,t=!1){if(null==e||"object"!=typeof e)return e;const r="function"==typeof window.structuredClone?window.structuredClone(e):Object.assign({},e),o=[null,void 0,""];t&&o.push(!1);for(const e in r)o.includes(r[e])?delete r[e]:Array.isArray(r[e])&&(r[e].length||delete r[e]);return r},L=function(e){return!["values","enableOther","other","label","subtype"].includes(e)},R=e=>Object.entries(e).map((([e,t])=>L(e)&&Object.values(I(e,t)).join(""))).filter(Boolean).join(" "),I=(e,t)=>{let r;return e=M(e),t&&(Array.isArray(t)?r=X(t.join(" ")):("boolean"==typeof t&&(t=t.toString()),r=X(t.trim()))),{name:e,value:t=t?`="${r}"`:""}},P=e=>e.reduce(((e,t)=>e.concat(Array.isArray(t)?P(t):t)),[]),M=e=>({className:"class"}[e]||B(e)),B=e=>(e=(e=e.replace(/[^\w\s\-]/gi,"")).replace(/([A-Z])/g,(function(e){return"-"+e.toLowerCase()}))).replace(/\s/g,"-").replace(/^-+/g,""),F=e=>e.replace(/-([a-z])/g,((e,t)=>t.toUpperCase())),q=function(){let e,t=0;return function(r){const o=Date.now();return o===e?++t:(t=0,e=o),(r.type||B(r.label))+"-"+o+"-"+t}}(),H=e=>void 0===e?e:[["array",e=>Array.isArray(e)],["node",e=>e instanceof window.Node||e instanceof window.HTMLElement],["component",()=>e&&e.dom],[typeof e,()=>!0]].find((t=>t[1](e)))[0],z=function(e,t="",r={}){let o=H(t);const{events:n}=r,i=T(r,k),l=document.createElement(e),a={string:e=>{O(l,l.innerHTML+e)},object:e=>{const{tag:t,content:r}=e,o=T(e,E);return l.appendChild(z(t,r,o))},node:e=>l.appendChild(e),array:e=>{for(let t=0;t<e.length;t++)o=H(e[t]),a[o](e[t])},function:e=>{e=e(),o=H(e),a[o](e)},undefined:()=>{}};for(const e in i)if(i.hasOwnProperty(e)){const t=M(e);let r=Array.isArray(i[e])?Z(i[e].join(" ").split(" ")).join(" "):i[e];if(x(t,r))continue;if("boolean"==typeof r){if(!0===r){const e="contenteditable"===t||t;l.setAttribute(t,e)}}else"id"!==t&&"name"!==t||(r=A(r)),void 0!==r&&l.setAttribute(t,r)}return t&&a[o](t),((e,t)=>{if(t)for(const r in t)t.hasOwnProperty(r)&&e.addEventListener(r,(e=>t[r](e)))})(l,n),l},U=e=>{const t=e.attributes,r={};return Q(t,(e=>{let o=t[e].value||"";o.match(/false|true/g)?o="true"===o:o.match(/undefined/g)&&(o=void 0),o&&(r[F(t[e].name)]=o)})),r},W=e=>{const t=[];for(let r=0;r<e.length;r++){const o=S(S({},U(e[r])),{},{label:e[r].textContent});t.push(o)}return t},$=e=>{const t=[];if(e.length){const r=e[0].getElementsByTagName("value");for(let e=0;e<r.length;e++)t.push(r[e].textContent)}return t},V=e=>{const t=(new window.DOMParser).parseFromString(e,"text/xml"),r=[];if(t){const e=t.getElementsByTagName("field");for(let t=0;t<e.length;t++){const o=U(e[t]),n=e[t].getElementsByTagName("option"),i=e[t].getElementsByTagName("userData");n&&n.length&&(o.values=W(n)),i&&i.length&&(o.userData=$(i)),r.push(o)}}return r},J=e=>{const t=document.createElement("textarea");return t.innerHTML=e,t.textContent},G=e=>{const t=document.createElement("textarea");return t.textContent=e,t.innerHTML},X=e=>{const t={'"':"&quot;","&":"&amp;","<":"&lt;",">":"&gt;"};return"string"==typeof e?e.replace(/["&<>]/g,(e=>t[e]||e)):e},Q=function(e,t,r){for(let o=0;o<e.length;o++)t.call(r,o,e[o])},Z=e=>e.filter(((e,t,r)=>r.indexOf(e)===t)),Y=(e,t)=>{const r=t.indexOf(e);r>-1&&t.splice(r,1)},K=(e,t="")=>{const r=jQuery;let o=[];return Array.isArray(e)||(e=[e]),_(e)||(o=jQuery.map(e,(e=>{const r={dataType:"script",cache:!0,url:(t||"")+e};return jQuery.ajax(r).done((()=>window.fbLoaded.js.push(e)))}))),o.push(jQuery.Deferred((e=>r(e.resolve)))),jQuery.when(...o)},_=(e,t="js")=>{const r=window.fbLoaded[t];return Array.isArray(e)?e.every((e=>r.includes(e))):r.includes(e)},ee=(t,r="")=>{Array.isArray(t)||(t=[t]),t.forEach((t=>{let o="href",n=t,i="";if("object"==typeof t&&(o=t.type||(t.style?"inline":"href"),i=t.id,n=i||t.href||t.style,t="inline"===o?t.style:t.href),!_(n,"css")){if("href"===o){const e=document.createElement("link");e.type="text/css",e.rel="stylesheet",e.href=(r||"")+t,document.head.appendChild(e)}else e(`<style type="text/css">${t}</style>`).attr("id",i).appendTo(e(document.head));window.fbLoaded.css.push(n)}}))},te=e=>e.replace(/\b\w/g,(function(e){return e.toUpperCase()})),re=(e,t)=>{const r=Object.assign({},e,t);for(const o in t)r.hasOwnProperty(o)&&(Array.isArray(t[o])?r[o]=Array.isArray(e[o])?Z(e[o].concat(t[o])):t[o]:"object"==typeof t[o]?r[o]=re(e[o],t[o]):r[o]=t[o]);return r},oe=(e,t,r)=>t.split(" ").forEach((t=>e.addEventListener(t,r,!1))),ne=(e,t)=>{const r=t.replace(".","");for(;(e=e.parentElement)&&!e.classList.contains(r););return e},ie=()=>{let e="";var t;return t=navigator.userAgent||navigator.vendor||window.opera,/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(t)&&(e="formbuilder-mobile"),e},le=e=>e.replace(/\s/g,"-").replace(/[^a-zA-Z0-9[\]_-]/g,""),ae=e=>e.replace(/[^0-9]/g,""),se=(e,t)=>t.filter((function(e){return!~this.indexOf(e)}),e),de=/^col-(xs|sm|md|lg)-([^\s]+)/,ce=e=>"string"==typeof e?e.split(" ").filter((e=>de.test(e)||e.startsWith("row-"))):[];function fe(...e){return e.find((e=>"number"==typeof e))}const ue={addEventListeners:oe,attrString:R,camelCase:F,capitalize:te,closest:ne,getContentType:H,escapeAttr:X,escapeAttrs:e=>{for(const t in e)e.hasOwnProperty(t)&&(e[t]=X(e[t]));return e},escapeHtml:G,forceNumber:ae,forEach:Q,getScripts:K,getStyles:ee,hyphenCase:B,isCached:_,markup:z,merge:re,mobileClass:ie,nameAttr:q,parsedHtml:J,parseXML:V,removeFromArray:Y,safeAttr:I,safeAttrName:M,safename:le,subtract:se,trimObj:D,unique:Z,validAttr:L,titleCase:function(e){const t=["a","an","and","as","at","but","by","for","for","from","in","into","near","nor","of","on","onto","or","the","to","with"].map((e=>`\\s${e}\\s`)),r=new RegExp(`(?!${t.join("|")})\\w\\S*`,"g");return`${e}`.replace(r,(e=>e.charAt(0).toUpperCase()+e.slice(1).replace(/[A-Z]/g,(e=>` ${e}`))))},firstNumberOrUndefined:fe,splitObject:(e,t)=>{const r=e=>(t,r)=>(t[r]=e[r],t);return[Object.keys(e).filter((e=>t.includes(e))).reduce(r(e),{}),Object.keys(e).filter((e=>!t.includes(e))).reduce(r(e),{})]}};e.fn.swapWith=function(t){const r=this,o=e(t),n=e("<div>");return r.before(n),o.before(r),n.before(o).remove(),r};var pe=ue;const me=["label","type"];class be{constructor(e,t){this.rawConfig=jQuery.extend({},e),e=jQuery.extend({},e),this.preview=t,delete e.isPreview,this.preview&&delete e.required;const r=["label","description","subtype","required","disabled"];for(const t of r)this[t]=e[t],delete e[t];e.id||(e.name?e.id=e.name:e.id="control-"+Math.floor(1e7*Math.random()+1)),this.id=e.id,this.type=e.type,this.description&&(e.title=this.description),be.controlConfig||(be.controlConfig={});const o=this.subtype?this.type+"."+this.subtype:this.type;this.classConfig=jQuery.extend({},be.controlConfig[this.type]||{},be.controlConfig[o]||{}),this.subtype&&(e.type=this.subtype),this.required&&(e.required="required",e["aria-required"]="true"),this.disabled&&(e.disabled="disabled"),this.config=e,this.configure()}static get definition(){return{}}static register(e,t,r){const o=r?r+".":"";be.classRegister||(be.classRegister={}),Array.isArray(e)||(e=[e]);for(const r of e)-1===r.indexOf(".")?be.classRegister[o+r]=t:be.error(`Ignoring type ${r}. Cannot use the character '.' in a type name.`)}static getRegistered(e=!1){const t=Object.keys(be.classRegister);return t.length?t.filter((t=>e?t.indexOf(e+".")>-1:-1===t.indexOf("."))):t}static getRegisteredSubtypes(){const e={};for(const t in be.classRegister)if(be.classRegister.hasOwnProperty(t)){const[r,o]=t.split(".");if(!o)continue;e[r]||(e[r]=[]),e[r].push(o)}return e}static getClass(e,t){const r=t?e+"."+t:e;return be.classRegister[r]||be.classRegister[e]||be.error("Invalid control type. (Type: "+e+", Subtype: "+t+"). Please ensure you have registered it, and imported it correctly.")}static loadCustom(e){let t=[];if(e&&(t=t.concat(e)),window.fbControls&&(t=t.concat(window.fbControls)),!this.fbControlsLoaded){for(const e of t)e(be,be.classRegister);this.fbControlsLoaded=!0}}static mi18n(e,t){const r=this.definition;let o=r.i18n||{};o=o[h().locale]||o.default||o;const n=this.camelCase(e),i="object"==typeof o?o[n]||o[e]:o;if(i)return i;let l=r.mi18n;return"object"==typeof l&&(l=l[n]||l[e]),l||(l=n),h().get(l,t)}static active(e){return!Array.isArray(this.definition.inactive)||-1===this.definition.inactive.indexOf(e)}static label(e){return this.mi18n(e)}static icon(e){const t=this.definition;return t&&"object"==typeof t.icon?t.icon[e]:t.icon}configure(){}build(){const e=this.config,{label:t,type:r}=e,o=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,me);return this.markup(r,J(t),o)}on(e){const t={prerender:e=>e,render:e=>{const t=()=>{this.onRender&&this.onRender(e)};this.css&&ee(this.css),this.js&&!_(this.js)?K(this.js).done(t):t()}};return e?t[e]:t}static error(e){throw new Error(e)}markup(e,t="",r={}){return this.element=z(e,t,r),this.element}parsedHtml(e){return J(e)}static camelCase(e){return F(e)}}const he=(e,t)=>{let r=e.id?`formbuilder-${e.type} form-group field-${e.id}`:"";if(e.className){const o=ce(e.className);o&&o.length>0&&(r+=` ${o.join(" ")}`,Array.isArray(t)||(t=[t]),t.forEach((e=>{e.classList&&e.classList.remove(...o),e.querySelectorAll("[class*=row-],[class*=col-]").forEach((e=>{e.classList&&e.classList.remove(...o)}))})))}return r};class ge{constructor(e,t=!1,r=!1,o={}){this.preview=null!=t&&t,this.disableHTMLLabels=null!=r&&r,this.controlConfig=null!=o?o:{},this.templates={label:null,help:null,default:(e,t,r,o)=>(r&&t.appendChild(r),this.markup("div",[t,e],{className:he(o,e)})),noLabel:(e,t,r,o)=>this.markup("div",e,{className:he(o,e)}),hidden:e=>e},e&&(this.templates=jQuery.extend(this.templates,e)),this.configure()}configure(){}build(e,t,r){this.preview&&(t.name?t.name=t.name+"-preview":t.name=pe.nameAttr(t)+"-preview"),t.id=t.name,this.data=jQuery.extend({},t),be.controlConfig=this.controlConfig;const o=new e(t,this.preview);let n=o.build();if("object"==typeof n&&n.field||(n={field:n}),"string"==typeof n.field){const e=this.markup("div",n.field,{});1===e.childElementCount?n.field=e.children.item(0):n.field=Array.from(e.children)}const i=this.label(),l=this.help();let a;a=r&&this.isTemplate(r)?r:this.isTemplate(n.layout)?n.layout:"default";const s=this.processTemplate(a,n.field,i,l);return o.on("prerender")(s),s.addEventListener("fieldRendered",o.on("render")),s}label(){const e=this.data.label||"",t=[this.disableHTMLLabels?document.createTextNode(e):pe.parsedHtml(e)];return this.data.required&&t.push(this.markup("span","*",{className:"formbuilder-required"})),this.isTemplate("label")?this.processTemplate("label",t):this.markup("label",t,{for:this.data.id,className:`formbuilder-${this.data.type}-label`})}help(){return this.data.description?this.isTemplate("help")?this.processTemplate("help",this.data.description):this.markup("span","?",{className:"tooltip-element",tooltip:this.data.description}):null}isTemplate(e){return"function"==typeof this.templates[e]}processTemplate(e,...t){let r=this.templates[e](...t,this.data);return r.jquery&&(r=r[0]),r}markup(e,t="",r={}){return pe.markup(e,t,r)}}const we=()=>null;h().addLanguage("en-US",{NATIVE_NAME:"English (US)",ENGLISH_NAME:"English",addOption:"Add Option +",allFieldsRemoved:"All fields were removed.",allowMultipleFiles:"Allow users to upload multiple files",allowSelect:"Allow selection",autocomplete:"Autocomplete",button:"Button",cannotBeEmpty:"This field cannot be empty",checkboxGroup:"Checkbox Group",checkbox:"Checkbox",checkboxes:"Checkboxes",className:"Class",clearAllMessage:"Are you sure you want to clear all fields?",clear:"Clear",close:"Close",content:"Content",copy:"Copy To Clipboard",copyButton:"&#43;",copyButtonTooltip:"Copy",dateField:"Date Field",description:"Help Text",descriptionField:"Description",devMode:"Developer Mode",editNames:"Edit Names",editorTitle:"Form Elements",editXML:"Edit XML",enableOther:"Enable &quot;Other&quot;",enableOtherMsg:"Let users enter an unlisted option",fieldDeleteWarning:"false",fieldVars:"Field Variables",fieldNonEditable:"This field cannot be edited.",fieldRemoveWarning:"Are you sure you want to remove this field?",fileUpload:"File Upload",formUpdated:"Form Updated",getStarted:"Drag a field from the right to this area",header:"Header",hide:"Edit",hidden:"Hidden Input",inline:"Inline",inlineDesc:"Display {type} inline",label:"Label",labelEmpty:"Field Label cannot be empty",limitRole:"Limit access to one or more of the following roles:",mandatory:"Mandatory",maxlength:"Max Length",minOptionMessage:"This field requires a minimum of 2 options",minSelectionRequired:"Minimum {min} selections required",multipleFiles:"Multiple Files",name:"Name",no:"No",noFieldsToClear:"There are no fields to clear",number:"Number",off:"Off",on:"On",option:"Option",optionCount:"Option {count}",options:"Options",optional:"optional",optionLabelPlaceholder:"Label",optionValuePlaceholder:"Value",optionEmpty:"Option value required",other:"Other",paragraph:"Paragraph",placeholder:"Placeholder","placeholders.value":"Value","placeholders.label":"Label","placeholders.email":"Enter your email","placeholders.className":"space separated classes","placeholders.password":"Enter your password",preview:"Preview",radioGroup:"Radio Group",radio:"Radio",removeMessage:"Remove Element",removeOption:"Remove Option",remove:"&#215;",required:"Required",reset:"Reset",requireValidOption:"Only accept a pre-defined Option",richText:"Rich Text Editor",roles:"Access",rows:"Rows",save:"Save",selectOptions:"Options",select:"Select",selectColor:"Select Color",selectionsMessage:"Allow Multiple Selections",size:"Size",sizes:"Sizes","size.xs":"Extra Small","size.sm":"Small","size.m":"Default","size.lg":"Large",step:"Step",style:"Style",styles:"Styles","styles.btn":"Button Styles","styles.btn.default":"Default","styles.btn.danger":"Danger","styles.btn.info":"Info","styles.btn.primary":"Primary","styles.btn.success":"Success","styles.btn.warning":"Warning",submit:"Submit",subtype:"Type",text:"Text Field",textArea:"Text Area",toggle:"Toggle",warning:"Warning!",value:"Value",viewJSON:"[{&hellip;}]",viewXML:"&lt;/&gt;",yes:"Yes"});const ye={actionButtons:[],allowStageSort:!0,append:!1,controlOrder:["autocomplete","button","checkbox-group","checkbox","date","file","header","hidden","number","paragraph","radio-group","select","text","textarea"],controlPosition:"right",dataType:"json",defaultFields:[],disabledActionButtons:[],disabledAttrs:[],disabledFieldButtons:{},disabledSubtypes:{},disableFields:[],disableHTMLLabels:!1,disableInjectedStyle:!1,editOnAdd:!1,fields:[],fieldRemoveWarn:!1,fieldEditContainer:null,inputSets:[],notify:{error:e=>{console.log(e)},success:e=>{console.log(e)},warning:e=>{console.warn(e)}},onAddField:(e,t)=>t,onAddFieldAfter:(e,t)=>t,onAddOption:e=>e,onClearAll:we,onCloseFieldEdit:we,onOpenFieldEdit:we,onSave:we,persistDefaultFields:!1,prepend:!1,replaceFields:[],roles:{1:"Administrator"},sanitizerOptions:{clobberingProtection:{document:!1,form:!1},backendOrder:[]},scrollToFieldOnAdd:!0,showActionButtons:!0,sortableControls:!1,stickyControls:{enable:!0,offset:{top:5,bottom:"auto",right:"auto"}},subtypes:{},templates:{},typeUserAttrs:{},typeUserDisabledAttrs:{},typeUserEvents:{},defaultGridColumnClass:"col-md-12",cancelGridModeDistance:100,enableColumnInsertMenu:!1,enableEnhancedBootstrapGrid:!1},ve={btn:["default","danger","info","primary","success","warning"]},xe={location:"assets/lang/"},Ae={},Ce={rowWrapperClass:"rowWrapper",colWrapperClass:"colWrapper",tmpRowPlaceholderClass:"tempRowWrapper",invisibleRowPlaceholderClass:"invisibleRowPlaceholder"};var Oe=o(395),ke=o.n(Oe);const Ee=["values"],je=["label","events"],Se=["actionButtons","replaceFields"];function Ne(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}class Te{constructor(e,t,r){this.data=p[e],this.d=i[e],this.config=Ae[e],this.doCancel=!1,this.layout=t,this.handleKeyDown=this.handleKeyDown.bind(this),this.formBuilder=r,this.toastTimer=null}startMoving(e,t){t.item.show().addClass("moving"),this.doCancel=!0,this.from=t.item.parent()}stopMoving(t,r){const o=this;r.item.removeClass("moving"),o.doCancel&&(r.sender&&e(r.sender).sortable("cancel"),this.from.closest(".frmb-control").sortable("cancel")),o.save(),o.doCancel=!1}beforeStop(e,t){const r=this,o=this.config.opts,n=r.d.stage.childNodes.length-1,i=[];r.stopIndex=t.placeholder.closest("ul.stage-wrap > *").index()-1,!o.sortableControls&&t.item.parent().hasClass("frmb-control")&&i.push(!0),i.push(t.item.is(":not(li.input-control,li.input-set-control)")&&!t.item.parent().hasClass("frmb-control")),o.prepend&&i.push(0===r.stopIndex),o.append&&i.push(r.stopIndex+1===n),r.doCancel=i.some((e=>!0===e))}getTypes(t){const r={type:t.attr("type")},o=e(".fld-subtype",t).val();return o!==r.type&&(r.subtype=o),r}fieldOptionData(t){const r=[],o=e(".sortable-options li",t);return o.each((e=>{const t=o[e],n=t.querySelectorAll("input[type=text], input[type=number], select"),i=t.querySelectorAll("input[type=checkbox], input[type=radio]"),l={};Q(n,(e=>{const t=n[e],r=t.dataset.attr;l[r]=t.value})),Q(i,(e=>{const t=i[e],r=t.getAttribute("data-attr");l[r]=t.checked})),r.push(l)})),r}xmlSave(e){const t=this.prepData(e),r=new XMLSerializer,o=["<fields>"];t.forEach((e=>{const{values:t}=e;let r=[`<field ${n=Ne(e,Ee),Object.entries(n).map((([e,t])=>`${B(e)}="${t}"`)).join(" ")}>`];var n;if(c.includes(e.type)){const e=t.map((e=>z("option",e.label,e).outerHTML));r=r.concat(e)}r.push("</field>"),o.push(r)})),o.push("</fields>");const n=z("form-template",P(o).join(""));return r.serializeToString(n)}prepData(t){const r=[],o=this.d,n=this,i=this.config;if(0!==t.childNodes.length){const l=[];Q(t.childNodes,(function(t,r){e(r).find("li.form-field").each((function(e,t){l.push(t)}))})),Q(t.childNodes,(function(t,r){const o=e(r);o.is("li")&&o.hasClass("form-field")&&l.push(r)})),l.length&&l.forEach((t=>{const l=e(t);if(!l.hasClass("disabled-field")){let a=n.getTypes(l);const s=e(".roles-field:checked",t),d=s.map((e=>s[e].value)).get();if(a=Object.assign({},a,n.getAttrVals(t)),a.subtype)if("quill"===a.subtype){const e=`${a.name}-preview`;if(window.fbEditors.quill[e]){const t=window.fbEditors.quill[e].instance.getContents();a.value=window.JSON.stringify(t.ops)}}else if("tinymce"===a.subtype&&window.tinymce){const e=`${a.name}-preview`,t=window.tinymce.get(e);t&&(a.value=t.getContent())}if(d.length&&(a.role=d.join(",")),a.className=a.className||a.class,a.className&&"true"==l.attr("addeddefaultcolumnclass")&&1==l.closest(this.formBuilder.rowWrapperClassSelector).children().length&&a.className.includes(i.opts.defaultGridColumnClass)){const e=ce(a.className);e&&e.length>0&&e.forEach((e=>{a.className=a.className.replace(e,"").trim()}))}if(a.className){const e=new RegExp("(?:^|\\s)btn-("+ve.btn.join("|")+")(?:\\s|$)","g").exec(a.className);e&&(a.style=e[1])}a=D(a),a.type&&a.type.match(o.optionFieldsRegEx)&&(a.values=n.fieldOptionData(l)),r.push(a)}}))}return r}getData(e){const t=this.data;if(e||(e=this.config.opts.formData),!e)return!1;const r={xml:e=>Array.isArray(e)?e:V(e),json:e=>"string"==typeof e?window.JSON.parse(e):e};return t.formData=r[this.config.opts.dataType](e)||[],t.formData}save(e=!1){const t=this,r=this.data,o=this.d.stage,n={xml:()=>t.xmlSave(o),json:e=>window.JSON.stringify(t.prepData(o),null,e&&"  ")};return r.formData=n[this.config.opts.dataType](e),document.dispatchEvent(w.formSaved),r.formData}incrementId(e){const t=e.lastIndexOf("-"),r=parseInt(e.substring(t+1))+1;return`${e.substring(0,t)}-${r}`}getAttrVals(t){const r=this.config,o=Object.create(null),n=t.querySelectorAll('[class*="fld-"]');return Q(n,(t=>{const i=n[t],l=F(i.getAttribute("name"));o[l]=[[i.attributes.contenteditable,()=>"xml"===r.opts.dataType?G(i.innerHTML):i.innerHTML],["checkbox"===i.type,()=>i.checked],["number"===i.type&&""!==i.value,()=>Number(i.value)],[i.attributes.multiple,()=>e(i).val()],[!0,()=>i.value]].find((([e])=>!!e))[1]()})),o}updatePreview(t){const r=this,o=this.d,n=t.attr("class"),i=t[0];if(n.includes("input-control"))return;const l=t.attr("type"),a=e(".prev-holder",i);let d=Object.assign({},r.getAttrVals(i),{type:l});l.match(o.optionFieldsRegEx)&&(d.values=[],d.multiple=e('[name="multiple"]',i).is(":checked"),e(".sortable-options li",i).each((function(t,r){const o={selected:e(".option-selected",r).is(":checked"),value:e(".option-value",r).val(),label:e(".option-label",r).val()};d.values.push(o)}))),d=D(d,!0),d.className=r.classNames(i,d),t.data("fieldData",d);const c=r.formBuilder.controls.custom.lookup(d.type),f=r.formBuilder.controls.custom.getClass(d.type),u=c?c.class:f||be.getClass(d.type,d.subtype),p=this.layout.build(u,d);s(a[0]),a[0].appendChild(p),p.dispatchEvent(w.fieldRendered)}disabledTT(e){const t=e.querySelectorAll(".disabled-field");Q(t,(e=>{const r=t[e],o=h().get("fieldNonEditable");if(o){const e=z("p",o,{className:"frmb-tt"});r.appendChild(e),r.addEventListener("mousemove",(t=>((e,t)=>{const r=t.field.getBoundingClientRect(),o=e.clientX-r.left-21,n=e.clientY-r.top-t.tt.offsetHeight-12;t.tt.style.transform=`translate(${o}px, ${n}px)`})(t,{tt:e,field:r})))}}))}classNames(t,r){const o=t.querySelector(".fld-className"),n=t.querySelector(".btn-style"),i=n&&n.value;if(!o)return;const{type:l}=r,a=o.multiple?e(o).val():o.value.trim().split(" "),s={button:"btn",submit:"btn"}[l];if(s&&i){for(let e=0;e<a.length;e++){const t=new RegExp(`^${s}-(?:`+ve.btn.join("|")+")$");a[e].match(t)&&a.splice(e,1,s+"-"+i)}a.push(s+"-"+i),a.push(s)}const d=Z(a).join(" ").trim();return o.value=d,d}closeConfirm(e,t){e||(e=document.getElementsByClassName("form-builder-overlay")[0]),e&&a(e),t||(t=document.getElementsByClassName("form-builder-dialog")[0]),t&&a(t),document.removeEventListener("keydown",this.handleKeyDown,!1),document.dispatchEvent(w.modalClosed)}handleKeyDown(e){27===(e.keyCode||e.which)&&(e.preventDefault(),this.closeConfirm.call(this))}showOverlay(){const e=z("div",null,{className:"form-builder-overlay"});return document.body.appendChild(e),e.classList.add("visible"),e.addEventListener("click",(({target:e})=>this.closeConfirm(e)),!1),document.addEventListener("keydown",this.handleKeyDown,!1),e}confirm(e,t,r=!1,o=""){const n=this,i=h().current,l=n.showOverlay(),a=z("button",i.yes,{className:"yes btn btn-success btn-sm"}),s=z("button",i.no,{className:"no btn btn-danger btn-sm"});s.onclick=function(){n.closeConfirm(l)},a.onclick=function(){t(),n.closeConfirm(l)};const d=z("div",[s,a],{className:"button-wrap"}),c=z("div",[e,d],{className:o="form-builder-dialog "+o});if(r)c.classList.add("positioned");else{const e=document.documentElement;r={pageX:Math.max(e.clientWidth,window.innerWidth||0)/2,pageY:Math.max(e.clientHeight,window.innerHeight||0)/2},c.style.position="fixed"}return c.style.left=r.pageX+"px",c.style.top=r.pageY+"px",document.body.appendChild(c),a.focus(),c}dialog(e,t=!1,r=""){const o=document.documentElement.clientWidth,n=document.documentElement.clientHeight;this.showOverlay();const i=z("div",e,{className:r="form-builder-dialog "+r});return t?i.classList.add("positioned"):(t={pageX:Math.max(o,window.innerWidth||0)/2,pageY:Math.max(n,window.innerHeight||0)/2},i.style.position="fixed"),i.style.left=t.pageX+"px",i.style.top=t.pageY+"px",document.body.appendChild(i),document.dispatchEvent(w.modalOpened),-1!==r.indexOf("data-dialog")&&document.dispatchEvent(w.viewData),i}confirmRemoveAll(t){const r=this,o=this.config,n=t.target.id.match(/frmb-\d{13}/)[0],i=document.getElementById(n),l=h().current,a=e("li.form-field",i),s=t.target.getBoundingClientRect(),d=document.body.getBoundingClientRect(),c={pageX:s.left+s.width/2,pageY:s.top-d.top-12};a.length?r.confirm(l.clearAllMessage,(()=>{r.removeAllFields.call(r,i),o.opts.persistDefaultFields&&o.opts.defaultFields?this.addDefaultFields():o.opts.notify.success(l.allFieldsRemoved),o.opts.onClearAll()}),c):r.dialog(l.noFieldsToClear,c)}addDefaultFields(){this.config.opts.defaultFields.forEach((e=>this.formBuilder.prepFieldVars(e))),this.d.stage.classList.remove("empty")}removeAllFields(e){const t=h().current,r=this.config.opts,o=[];e.querySelectorAll(this.formBuilder.fieldSelector).length&&(r.prepend&&o.push(!0),r.append&&o.push(!0),o.some(Boolean)||(e.classList.add("empty"),e.dataset.content=t.getStarted),this.emptyStage(e))}emptyStage(e){s(e).classList.remove("removing"),e.dispatchEvent(w.stageEmptied),this.save()}stageIsEmpty(){return 0===e(this.d.stage).find("li").length}setFieldOrder(t){if(!this.config.opts.sortableControls)return!1;const r=window.JSON,o=[];return t.children().each(((t,r)=>{const n=e(r).data("type");n&&o.push(n)})),ke()("sessionStorage")&&window.sessionStorage.setItem("fieldOrder",r.stringify(o)),o}closeAllEdit(){e(this.d.stage).find("li.form-field").each(((e,t)=>{this.closeField(t.id,!1)}))}toggleEdit(t,r=!0){const o=document.getElementById(t);if(o)return e(o).hasClass("editing")?this.closeField(t,r):this.openField(t,r)}closeField(t,r=!0){const o=this,n=document.getElementById(t);if(!n)return n;const i=e(".frm-holder",n),l=e(".prev-holder",n);let a=!1;if(e(n).hasClass("editing")&&(a=!0),!a)return n;n.classList.toggle("editing"),e(".toggle-form",n).toggleClass("open"),r?(l.slideToggle(250),i.slideToggle(250)):(l.toggle(),i.toggle()),this.updatePreview(e(n));const s=e(`#${t}`),d=e(`#${t}-cont`);d.append(s),this.removeContainerProtection(d.attr("id")),this.config.opts.onCloseFieldEdit(i[0]),document.dispatchEvent(w.fieldEditClosed);const c=s.find(".prev-holder"),f=setTimeout((()=>{clearTimeout(f),o.tmpCleanPrevHolder(c).forEach((e=>{if(e.columnInfo.columnSize){const t=o.getBootstrapColumnClass(d.attr("class"));t!==e.columnInfo.columnSize&&(d.removeClass(t).addClass(e.columnInfo.columnSize),o.tmpCleanPrevHolder(c))}}))}),300);return n}openField(t,r=!0){const o=document.getElementById(t);if(!o)return o;const n=e(".frm-holder",o),i=e(".prev-holder",o);let l=!1;if(e(o).hasClass("editing")&&(l=!0),l)return o;o.classList.toggle("editing"),e(".toggle-form",o).toggleClass("open"),r?(i.slideToggle(250),n.slideToggle(250)):(i.toggle(),n.toggle()),this.updatePreview(e(o));const a=e(`#${t}`),s=e(`#${t}-cont`),d=s.closest(this.formBuilder.rowWrapperClassSelector);return this.formBuilder.preserveTempContainers.push(s.attr("id")),a.insertAfter(d),this.formBuilder.currentEditPanel=n[0],this.config.opts.onOpenFieldEdit(n[0]),document.dispatchEvent(w.fieldEditOpened),e(document).trigger("fieldOpened",[{rowWrapperID:d.attr("id")}]),o}getStyle(e,t=!1){let r;return window.getComputedStyle?r=window.getComputedStyle(e,null):e.currentStyle&&(r=e.currentStyle),t?r[t]:r}showData(){const e=G(this.getFormData(this.config.opts.dataType,!0)),t=z("code",e,{className:`formData-${this.config.opts.dataType}`});this.dialog(z("pre",t),!1,"data-dialog")}removeField(t,r=250){let o=!1;const n=this,i=this.d.stage,l=i.getElementsByClassName("form-field");if(!l.length)return this.config.opts.notify.warning("No fields to remove"),!1;if(!t){const e=[].slice.call(l).map((e=>e.id));this.config.opts.notify.warning("fieldID required to remove specific fields."),this.config.opts.notify.warning("Removing last field since no ID was supplied."),this.config.opts.notify.warning("Available IDs: "+e.join(", ")),t=e[e.length-1]}const a=document.getElementById(t);if(!a)return this.config.opts.notify.warning("Field not found"),!1;const s=e(a),d=s.closest(this.formBuilder.rowWrapperClassSelector);s.slideUp(r,(function(){s.removeClass("deleting"),s.remove(),o=!0,n.save(),i.childNodes.length||(i.classList.add("empty"),i.dataset.content=h().current.getStarted)}));const c=Object.assign({},this.config.opts.typeUserEvents["*"],this.config.opts.typeUserEvents[a.type]);if(c&&c.onremove&&c.onremove(a),document.dispatchEvent(w.fieldRemoved),d.length){this.removeContainerProtection(`${t}-cont`);const r=setTimeout((()=>{clearTimeout(r),e(document).trigger("checkRowCleanup",[{rowWrapperID:d.attr("id")}])}),333)}return o}processActionButtons(e){const{label:t,events:r}=e,o=Ne(e,je);let n=t;const i=this.data;n=n?h().current[n]||n:o.id?h().current[o.id]||te(o.id):"",o.id?o.id=`${i.formID}-${o.id}-action`:o.id=`${i.formID}-action-${Math.round(1e3*Math.random())}`;const l=z("button",n,o);if(r)for(const e in r)r.hasOwnProperty(e)&&l.addEventListener(e,(t=>r[e](t)));return l}processSubtypes(e){const t=this.config.opts.disabledSubtypes;for(const t in e)e.hasOwnProperty(t)&&be.register(e[t],be.getClass(t),t);const r=be.getRegisteredSubtypes(),o=Object.entries(r).reduce(((e,[r,o])=>(e[r]=t[r]&&se(t[r],o)||o,e)),{}),n={};for(const e in o)if(o.hasOwnProperty(e)){const t=[];for(const r of o[e]){const o=be.getClass(e,r),n=o.mi18n(`subtype.${r}`)||o.mi18n(r)||r;t.push({label:n,value:r})}n[e]=t}return n}editorUI(e,t){const r=this.d,o=this.data,n=e||o.formID,i="left"===(t||"")?"controls-left":"controls-right";r.editorWrap=z("div",null,{id:`${o.formID}-form-wrap`,className:`form-wrap form-builder formbuilder-embedded-bootstrap ${ie()} ${i}`}),r.stage=z("ul",null,{id:n,className:"frmb stage-wrap"}),r.controls=z("ul",null,{id:`${n}-control-box`,className:"frmb-control"});const l=this.formActionButtons();r.formActions=z("div",l,{className:"form-actions btn-group"})}formActionButtons(){const e=this.config.opts;return e.actionButtons.map((t=>{if(t.id&&-1===e.disabledActionButtons.indexOf(t.id))return this.processActionButtons(t)})).filter(Boolean)}processOptions(e){const t=this,{actionButtons:r,replaceFields:o}=e,n=Ne(e,Se);let i=n.fieldEditContainer;"string"==typeof n.fieldEditContainer&&(i=document.querySelector(n.fieldEditContainer));const l=[{type:"button",id:"clear",className:"clear-all btn btn-danger",events:{click:t.confirmRemoveAll.bind(t)}},{type:"button",label:"viewJSON",id:"data",className:"btn btn-default get-data",events:{click:t.showData.bind(t)}},{type:"button",id:"save",className:"btn btn-primary save-template",events:{click:e=>{t.save(),t.config.opts.onSave(e,t.data.formData)}}}].concat(r);return n.fields=n.fields.concat(o),n.disableFields=n.disableFields.concat(o.map((({type:e})=>e&&e))),"xml"===n.dataType&&(n.disableHTMLLabels=!0),t.config.opts=Object.assign({},{actionButtons:l},{fieldEditContainer:i},n),t.config.opts}input(e={}){return z("input",null,e)}getFormData(e="js",t=!1){const r=this,o={js:()=>r.prepData(r.d.stage),xml:()=>r.xmlSave(r.d.stage),json:e=>window.JSON.stringify(r.prepData(r.d.stage),null,e&&"  ")};return o[e](t)}tmpCleanPrevHolder(t){const r=this,o=[],n=t.find(".form-group");function i(e){const t=e.attr("class");if(void 0!==t&&!1!==t){const t=r.tryParseColumnInfo(e[0]);e.attr("class",e.attr("class").replace("__fb-tmp-col-","col-")),e.attr("class",e.attr("class").replace("__fb-tmp-row-","row-")),e.attr("class",e.attr("class").replace("col-","__fb-tmp-col-")),e.attr("class",e.attr("class").replace("row-","__fb-tmp-row-"));const n={};n.field=e,n.columnInfo=t,o.push(n)}}return i(n),n.find("*").each((function(t,r){i(e(r))})),o}tryParseColumnInfo(e){const t={};if(e.className){const r=ce(e.className);r&&r.length>0&&r.forEach((e=>{e.startsWith("row-")?t.rowUniqueId=e.replace("row-","").trim():t.columnSize=e}))}return t}removeContainerProtection(e){const t=this.formBuilder.preserveTempContainers.indexOf(e);-1!==t&&this.formBuilder.preserveTempContainers.splice(t,1)}toggleHighlight(e,t=600){e.addClass("moveHighlight"),setTimeout((function(){e.removeClass("moveHighlight")}),t)}showToast(t,r=3e3){null!=this.toastTimer&&(window.clearTimeout(this.toastTimer),this.toastTimer=null),this.toastTimer=setTimeout((function(){e(".snackbar").removeClass("show")}),r),e(".snackbar").addClass("show").html(t)}getDistanceBetweenPoints(e,t,r,o){const n=r-e,i=o-t;return Math.floor(Math.sqrt(i*i+n*n))}getRowClass(e){if(e){const t=e.split(" ").filter((e=>e.startsWith("row-")));if(t&&t.length>0)return t[0]}return""}getRowValue(e){if(e){const t=this.getRowClass(e);if(t)return t.split("-")[1]}return"0"}changeRowClass(e,t){const r=this.getRowClass(e);return e.replace(r,`row-${t}`)}getBootstrapColumnValue(e){if(e){const t=this.getBootstrapColumnClass(e);if(t)return parseInt(t.split("-")[2])}return 0}getBootstrapColumnPrefix(e){if(e){const t=this.getBootstrapColumnClass(e);if(t)return`${t.split("-")[0]}-${t.split("-")[1]}`}return""}getBootstrapColumnClass(e){if(e){const t=e.split(" ").filter((e=>de.test(e)));if(t&&t.length>0)return t[0]}return""}changeBootstrapClass(e,t){const r=this.getBootstrapColumnClass(e);return e.replace(r,`${this.getBootstrapColumnPrefix(e)}-${t}`)}syncBootstrapColumnWrapperAndClassProperty(t,r){const o=e(`#${t}-cont`);o.attr("class",this.changeBootstrapClass(o.attr("class"),r));const n=e(`#className-${t}`);n.val()&&n.val(this.changeBootstrapClass(n.val(),r))}syncFieldWithNewRow(t,r){if(t){const n=e(t).find(".fld-className"),i=n.val()?.trim();if(i){var o;let e=i.split(" ");const t=this.getRowClass(i),l=this.getRowClass(null!==(o=r?.className)&&void 0!==o?o:"");t!==l&&(t&&(e=e.filter((function(e){return e!==t}))),l&&e.push(l),n.val(e.join(" ")))}}}}const De=["values","type"];be.register("autocomplete",class extends be{static get definition(){return{mi18n:{requireValidOption:"requireValidOption"}}}build(){const e=this.config,{values:t,type:r}=e,o=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,De),n=e=>{const t=e.target.nextSibling.nextSibling,r=e.target.nextSibling,o=this.getActiveOption(t);let n=new Map([[38,()=>{const e=this.getPreviousOption(o);e&&this.selectOption(t,e)}],[40,()=>{const e=this.getNextOption(o);e&&this.selectOption(t,e)}],[13,()=>{o?(e.target.value=o.innerHTML,r.value=o.getAttribute("value"),"none"===t.style.display?this.showList(t,o):this.hideList(t)):this.config.requireValidOption&&(this.isOptionValid(t,e.target.value)||(e.target.value="",e.target.nextSibling.value="")),e.preventDefault()}],[27,()=>{this.hideList(t)}]]).get(e.keyCode);return n||(n=()=>!1),n()},i={focus:e=>{const t=e.target.nextSibling.nextSibling,r=d(t.querySelectorAll("li"),e.target.value);if(e.target.addEventListener("keydown",n),e.target.value.length>0){const e=r.length>0?r[r.length-1]:null;this.showList(t,e)}},blur:e=>{e.target.removeEventListener("keydown",n);const t=setTimeout((()=>{e.target.nextSibling.nextSibling.style.display="none",clearTimeout(t)}),200);if(this.config.requireValidOption){const t=e.target.nextSibling.nextSibling;this.isOptionValid(t,e.target.value)||(e.target.value="",e.target.nextSibling.value="")}},input:e=>{const t=e.target.nextSibling.nextSibling;e.target.nextSibling.value=e.target.value;const r=d(t.querySelectorAll("li"),e.target.value);if(0==r.length)this.hideList(t);else{let e=this.getActiveOption(t);e||(e=r[r.length-1]),this.showList(t,e)}}},l=Object.assign({},o,{id:`${o.id}-input`,autocomplete:"off",events:i}),a=Object.assign({},o,{type:"hidden"});delete l.name;const s=[this.markup("input",null,l),this.markup("input",null,a)],c=t.map((e=>{const t=e.label,r={events:{click:t=>{const r=t.target.parentElement,o=r.previousSibling.previousSibling;o.value=e.label,o.nextSibling.value=e.value,this.hideList(r)}},value:e.value};return this.markup("li",t,r)}));return s.push(this.markup("ul",c,{id:`${o.id}-list`,className:`formbuilder-${r}-list`})),s}hideList(e){this.selectOption(e,null),e.style.display="none"}showList(e,t){this.selectOption(e,t),e.style.display="block",e.style.width=e.parentElement.offsetWidth+"px"}getActiveOption(e){const t=e.getElementsByClassName("active-option")[0];return t&&"none"!==t.style.display?t:null}getPreviousOption(e){let t=e;do{t=t?t.previousSibling:null}while(null!=t&&"none"===t.style.display);return t}getNextOption(e){let t=e;do{t=t?t.nextSibling:null}while(null!=t&&"none"===t.style.display);return t}selectOption(e,t){const r=e.querySelectorAll("li");for(let e=0;e<r.length;e++)r[e].classList.remove("active-option");t&&t.classList.add("active-option")}isOptionValid(e,t){const r=e.querySelectorAll("li");let o=!1;for(let e=0;e<r.length;e++)if(r[e].innerHTML===t){o=!0;break}return o}onRender(t){if(this.config.userData){const t=e("#"+this.config.name),r=t.next(),o=this.config.userData[0];let n=null;if(r.find("li").each((function(){e(this).attr("value")===o&&(n=e(this).get(0))})),null===n)return this.config.requireValidOption?void 0:void t.prev().val(this.config.userData[0]);t.prev().val(n.innerHTML),t.val(n.getAttribute("value"));const i=t.next().get(0);"none"===i.style.display?this.showList(i,n):this.hideList(i)}return t}});class Le extends be{build(){return{field:this.markup("button",this.label,this.config),layout:"noLabel"}}}be.register("button",Le),be.register(["button","submit","reset"],Le,"button");class Re extends be{constructor(e,t,r){super(e,t),this.template=r}build(){let e=this.template;if(!e)return be.error(`Invalid custom control type '${this.type}'. Please ensure you have registered it correctly as a template option.`);const t=Object.assign(this.config),r=["label","description","subtype","id","preview","required","title","aria-required","type"];for(const e of r)t[e]=this.config[e]||this[e];return e=e.bind(this),e=e(t),e.js&&(this.js=e.js),e.css&&(this.css=e.css),this.onRender=e.onRender,{field:e.field,layout:e.layout}}}be.register("hidden",class extends be{build(){return this.field=this.markup("input",null,this.config),{field:this.field,layout:"hidden"}}onRender(){this.config.userData&&e(this.field).val(this.config.userData[0])}});const Ie=["type"];class Pe extends be{build(){const e=this.config,{type:t}=e,r=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,Ie);let o=t;const n={paragraph:"p",header:this.subtype};return n[t]&&(o=n[t]),{field:this.markup(o,pe.parsedHtml(this.label),r),layout:"noLabel"}}}be.register(["paragraph","header"],Pe),be.register(["p","address","blockquote","canvas","output"],Pe,"paragraph"),be.register(["h1","h2","h3","h4","h5","h6"],Pe,"header");const Me=["values","value","placeholder","type","inline","other","toggle"],Be=["label"];function Fe(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}be.register(["select","checkbox-group","radio-group","checkbox"],class extends be{static get definition(){return{inactive:["checkbox"],mi18n:{minSelectionRequired:"minSelectionRequired"}}}build(){const e=[],t=this.config,{values:r,value:o,placeholder:n,type:i,inline:l,other:a,toggle:s}=t,d=Fe(t,Me),c=i.replace("-group",""),f="select"===i;if((d.multiple||"checkbox-group"===i)&&(d.name=d.name+"[]"),("checkbox-group"===i||"radio-group"===i)&&d.required){const e=this,t=this.onRender.bind(this);this.onRender=function(){t(),e.groupRequired()}}if(delete d.title,r){n&&f&&e.push(this.markup("option",n,{disabled:!0,selected:!0,value:""}));for(let t=0;t<r.length;t++){let i=r[t];"string"==typeof i&&(i={label:i,value:i});const{label:a=""}=i,u=Fe(i,Be);if(u.id=`${d.id}-${t}`,u.selected&&!n||delete u.selected,void 0!==o&&u.value===o&&(u.selected=!0),f){const t=this.markup("option",document.createTextNode(a),u);e.push(t)}else{const t=[a];let r=`formbuilder-${c}`;l&&(r+="-inline"),u.type=c,u.selected&&(u.checked="checked",delete u.selected);const o=this.markup("input",null,Object.assign({},d,u)),n={for:u.id};let i=[o,this.markup("label",t,n)];s&&(delete n.for,n.className="kc-toggle",t.unshift(o,this.markup("span")),i=this.markup("label",t,n));const f=this.markup("div",i,{className:r});e.push(f)}}if(!f&&a){var u;const t={id:`${d.id}-other`,className:`${null!==(u=d.className)&&void 0!==u?u:""} other-option`,value:""};let r=`formbuilder-${c}`;l&&(r+="-inline");const o=Object.assign({},d,t);o.type=c;const n={type:"text",events:{input:e=>{const t=e.target;t.parentElement.previousElementSibling.value=t.value}},id:`${t.id}-value`,className:"other-val"},i=this.markup("input",null,o),a=[document.createTextNode(be.mi18n("other")),this.markup("input",null,n)],s=this.markup("label",a,{for:o.id}),f=this.markup("div",[i,s],{className:r});e.push(f)}}return this.dom="select"==i?this.markup(c,e,D(d,!0)):this.markup("div",e,{className:i}),this.dom}groupRequired(){const e=this.element.getElementsByTagName("input"),t=this.element.querySelectorAll("input:not([type=text])"),r=this.element.querySelector(".other-option"),o=this.element.querySelector(".other-val"),n=()=>{const e=[].some.call(t,(e=>e.checked));((e,t,r,o)=>{[].forEach.call(e,(e=>{o?e.removeAttribute("required"):e.setAttribute("required","required"),((e,t)=>{const r=be.mi18n("minSelectionRequired",1);t?e.setCustomValidity(""):e.setCustomValidity(r)})(e,o)})),t&&(t.checked?r.setAttribute("required","required"):r.removeAttribute("required"))})(t,r,o,e)};for(let t=e.length-1;t>=0;t--)e[t].addEventListener("change",n);n()}onRender(){if(this.config.userData){const t=this.config.userData.slice();"select"===this.config.type?e(this.dom).val(t).prop("selected",!0):this.config.type.endsWith("-group")&&("checkbox-group"===this.config.type&&this.dom.querySelectorAll("input[type=checkbox]").forEach((e=>{e.removeAttribute("checked")})),this.dom.querySelectorAll("input").forEach((e=>{if(!e.classList.contains("other-val")){for(let r=0;r<t.length;r++)if(e.value===t[r]){e.setAttribute("checked","checked"),t.splice(r,1);break}if(e.id.endsWith("-other")&&t.length>0){const r=this.dom.querySelector(`#${e.id}-value`);e.setAttribute("checked","checked"),r.value=e.value=t[0],r.style.display="inline-block"}}})))}}});class qe extends be{static get definition(){return{mi18n:{date:"dateField",file:"fileUpload"}}}build(){let{name:e}=this.config;e=this.config.multiple?`${e}[]`:e;const t=Object.assign({},this.config,{name:e});return this.dom=this.markup("input",null,t),this.dom}onRender(){this.config.userData&&e(this.dom).val(this.config.userData[0])}}be.register(["text","file","date","number"],qe),be.register(["text","password","email","color","tel"],qe,"text"),be.register(["date","time","datetime-local"],qe,"date"),be.register(["number","range"],qe,"number");const He=["value"];class ze extends be{static get definition(){return{mi18n:{textarea:"textArea"}}}build(){const e=this.config,{value:t=""}=e,r=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,He);return delete r.type,this.field=this.markup("textarea",this.parsedHtml(t),r),this.field}onRender(){this.config.userData&&e(this.field).val(this.config.userData[0])}on(t){return"prerender"==t&&this.preview?t=>{this.field&&(t=this.field),e(t).on("mousedown",(e=>{e.stopPropagation()}))}:super.on(t)}}be.register("textarea",ze),be.register("textarea",ze,"textarea");const Ue=["value"];ze.register("tinymce",class extends ze{configure(){if(this.js=[],window.tinymce||this.js.push("https://cdnjs.cloudflare.com/ajax/libs/tinymce/4.9.11/tinymce.min.js"),this.classConfig.js){let e=this.classConfig.js;Array.isArray(e)||(e=new Array(e)),this.js=this.js.concat(e),delete this.classConfig.js}this.classConfig.css&&(this.css=this.classConfig.css),this.editorOptions={height:250,paste_data_images:!0,plugins:["advlist","autolink","lists","link","image","charmap","print","preview","anchor","searchreplace","visualblocks","code","fullscreen","insertdatetime","media","table","contextmenu","paste","code"],toolbar:"undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | table"}}build(){const e=this.config,{value:t=""}=e,r=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,Ue);return delete r.type,this.field=this.markup("textarea",this.parsedHtml(t),r),r.disabled&&(this.editorOptions.readonly=!0),this.field}onRender(){const e=window.tinymce.get(this.id);e&&window.tinymce.remove(e);const t=jQuery.extend(this.editorOptions,this.classConfig);t.target=this.field;const r=[];Number(window.tinymce.majorVersion)>=5&&r.push("contextmenu"),Number(window.tinymce.majorVersion)>=6&&r.push("paste","print"),t.plugins=t.plugins.filter((e=>-1===r.indexOf(e)));const o=this.config.userData?this.parsedHtml(this.config.userData[0]):void 0,n=window.lastFormBuilderCopiedTinyMCE?this.parsedHtml(window.lastFormBuilderCopiedTinyMCE):void 0;window.lastFormBuilderCopiedTinyMCE=null;const i=function(e){n?e[0].setContent(n):o&&e[0].setContent(o)};setTimeout((()=>{window.tinymce.init(t).then(i)}),0)}},"textarea");const We=["value"];function $e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function Ve(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$e(Object(r),!0).forEach((function(t){Je(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$e(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Je(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,"string");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}ze.register("quill",class extends ze{configure(){const e={modules:{toolbar:[[{header:[1,2,!1]}],["bold","italic","underline"],["code-block"]]},placeholder:this.config.placeholder||"",theme:"snow"},[t,r]=pe.splitObject(this.classConfig,["css","js"]);Object.assign(this,Ve(Ve({},{js:"https://cdn.quilljs.com/1.2.4/quill.js",css:"https://cdn.quilljs.com/1.2.4/quill.snow.css"}),t)),this.editorConfig=Ve(Ve({},e),r)}build(){const e=this.config,{value:t=""}=e,r=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,We);return delete r.type,this.field=this.markup("div",null,r),this.field.classList.contains("form-control")&&this.field.classList.remove("form-control"),this.field}onRender(e){const t=this.config.value||"",r=window.Quill.import("delta");window.fbEditors.quill[this.id]={};const o=window.fbEditors.quill[this.id];return o.instance=new window.Quill(this.field,this.editorConfig),o.data=new r,t&&o.instance.setContents(window.JSON.parse(this.parsedHtml(t))),o.instance.on("text-change",(function(e){o.data=o.data.compose(e)})),e}},"textarea");class Ge{constructor(e={},t=[]){this.customRegister={},this.templateControlRegister={},this.def={icon:{},i18n:{}},this.register(e,t)}register(e={},t=[]){const r=h().locale;this.def.i18n[r]||(this.def.i18n[r]={});const o=this;Object.keys(e).forEach((t=>{const r=function(r,o){this.customControl=new Re(r,o,e[t]),this.build=function(){return this.customControl.build()},this.on=function(e){return this.customControl.on(e)}};r.definition={},r.label=e=>o.label(e),r.icon=e=>o.icon(e),this.templateControlRegister[t]=r}));for(const o of t){let t=o.type;if(o.attrs=o.attrs||{},!t){if(!o.attrs.type){be.error("Ignoring invalid custom field definition. Please specify a type property.");continue}t=o.attrs.type}let n=o.subtype||t;if(e[t]){const e=this.templateControlRegister[t];e.definition=o,this.customRegister[n]=jQuery.extend(o,{type:t,class:e})}else try{const e=be.getClass(t,o.subtype);n=o.datatype?o.datatype:`${t}-${Math.floor(9e3*Math.random()+1e3)}`,this.customRegister[n]=jQuery.extend(o,{type:t,class:e})}catch(e){be.error("Error while registering custom field: "+t+(o.subtype?":"+o.subtype:"")+". Unable to find any existing defined control or template for rendering.")}this.def.i18n[r][n]=Array.isArray(o.label)?h().get(...o.label)||o.label[0]:o.label,this.def.icon[n]=o.icon}}label(e){const t=this.def;let r=t.i18n||{};r=r[h().locale]||r.default||r;const o=be.camelCase(e),n="object"==typeof r?r[o]||r[e]:r;if(n)return n;{let r=t.mi18n;return"object"==typeof r&&(r=r[o]||r[e]),r||(r=o),h().get(r)}}get definition(){return{}}icon(e){const t=this.def;return t&&"object"==typeof t.icon?t.icon[e]:t.icon}getRegistered(e=!1){var t;return e?null!==(t=this.templateControlRegister[e])&&void 0!==t?t:void 0:Object.keys(this.customRegister)}getClass(e){var t;return null!==(t=this.templateControlRegister[e])&&void 0!==t?t:void 0}lookup(e){return this.customRegister[e]}}var Xe=JSON.parse('{"fn":"formbuilder-icon-"}');const Qe=Xe.fn;class Ze{constructor(e,t){this.opts=e,this.dom=t.controls,this.getRegistered=be.getRegistered,this.init()}init(){this.setupControls(),this.appendControls()}setupControls(){const e=this.opts;be.loadCustom(e.controls),this.custom=new Ge(e.templates,e.fields);const t=be.getRegistered(),r=this.custom.getRegistered();r&&jQuery.merge(t,r),this.registeredSubtypes=be.getRegisteredSubtypes(),e.sortableControls&&this.dom.classList.add("sort-enabled"),this.controlList=[],this.allControls={};for(let e=0;e<t.length;e++){const r=t[e];let o,n,i=this.custom.lookup(r);if(i)o=i.class,n=this.custom.label(r);else{if(i={},o=be.getClass(r),!o||!o.active(r))continue;n=o.label(r)}const l=i.icon||o.icon(r),a=l?"":i.iconClassName||`${Qe+r.replace(/-[\d]{4}$/,"")}`;l&&(n=`<span class="control-icon">${l}</span>${n}`);const s=z("li",z("span",n),{className:`${a} input-control input-control-${e}`});s.dataset.type=r,this.controlList.push(r),this.allControls[r]=s}e.inputSets.length&&e.inputSets.forEach(((e,t)=>{let{name:r,label:o}=e;r=r||B(o),e.icon&&(o=`<span class="control-icon">${e.icon}</span>${o}`);const n=z("li",z("span",o),{className:`input-set-control input-set-${t}`});n.dataset.type=r,this.controlList.push(r),this.allControls[r]=n}))}orderFields(e){const t=this.opts,r=t.controlOrder.concat(e);let o;return ke()("sessionStorage")&&(t.sortableControls?o=window.sessionStorage.getItem("fieldOrder"):window.sessionStorage.removeItem("fieldOrder")),o?(o=window.JSON.parse(o),o=Z(o.concat(e)),o=Object.keys(o).map((e=>o[e]))):o=Z(r),o.forEach((e=>{const t=new RegExp("-[\\d]{4}$");if(e.match(t)){const r=o.indexOf(e.replace(t,""));-1!==r&&(o.splice(o.indexOf(e),1),o.splice(r+1,o.indexOf(e),e))}})),t.disableFields.length&&(o=o.filter((e=>!t.disableFields.includes(e)))),o.filter(Boolean)}appendControls(){const e=document.createDocumentFragment();s(this.dom),this.orderFields(this.controlList).forEach((t=>{const r=this.allControls[t];r&&e.appendChild(r)})),this.dom.appendChild(e)}getClass(e,t){return this.custom.getClass(e)||be.getClass(e,t)}}const Ye=["class","className"],Ke=["multiple","options","label","value","class","className"],_e=["class","className"],et=["tag","content"],tt=["i18n"];function rt(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function ot(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function nt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ot(Object(r),!0).forEach((function(t){it(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ot(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function it(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,"string");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const lt=Xe.fn,{rowWrapperClass:at,colWrapperClass:st,tmpRowPlaceholderClass:dt,invisibleRowPlaceholderClass:ct}=Ce,{rowWrapperClassSelector:ft,colWrapperClassSelector:ut,tmpRowPlaceholderClassSelector:pt,invisibleRowPlaceholderClassSelector:mt}=(bt=Ce,Object.entries(bt).reduce(((e,[t,r])=>S(S({},e),{},{[`${t}Selector`]:`.${r}`})),{}));var bt;function ht(e,t,o){const n=this,i=h().current,l=`frmb-${Date.now()}`,s=new m(l),d=new u(l),c=Ae[l]={};let f=[];n.preserveTempContainers=[],n.rowWrapperClassSelector=ft,n.colWrapperClassSelector=ut,n.colWrapperClass=st,n.fieldSelector=e.enableEnhancedBootstrapGrid?ft:"li.form-field",(e=>{if("object"!=typeof e)throw"Invalid value given to setSanitizerConfig, expected config object";if(e.hasOwnProperty("clobberingProtection")&&["document","form","namespaceAttributes"].forEach((t=>{e.clobberingProtection.hasOwnProperty(t)&&"boolean"==typeof e.clobberingProtection[t]&&(y.clobberingProtection[t]=e.clobberingProtection[t])})),e.hasOwnProperty("backends")){if("object"!=typeof e.backends)throw"backends config expected to be an Object";Object.keys(e.backends).forEach((t=>y.backends[t]=e.backends[t]))}if(e.hasOwnProperty("backendOrder")){if(y.backendOrder=[],!Array.isArray(e.backendOrder))throw"backendOrder config expected to be an Array of backend keys as strings";e.backendOrder.forEach((e=>{if(!y.backends.hasOwnProperty(e))throw"unknown sanitizer backend "+e;y.backendOrder.push(e)}))}})(e.sanitizerOptions),o(t).closest("form").length&&e.notify.warning("WARNING: FormBuilder does not support being contained with a <form> Element"),e.layout||(e.layout=ge);const p=new e.layout(e.layoutTemplates,!0,e.disableHTMLLabels,e.controlConfig),b=new Te(l,p,n),g=z;e=b.processOptions(e),b.editorUI(l,e.controlPosition),s.formID=l,s.lastID=`${s.formID}-fld-0`;const v=new Ze(e,d);n.controls=v;const x=c.subtypes=b.processSubtypes(e.subtypes),A=o(d.stage),C=o(d.controls);let k,E,j=!1,S=!1,N=!1;function T(){return!!e.enableEnhancedBootstrapGrid}A.sortable({cursor:"move",opacity:.9,revert:150,beforeStop:(e,t)=>b.beforeStop.call(b,e,t),start:(e,t)=>b.startMoving.call(b,e,t),stop:(e,t)=>b.stopMoving.call(b,e,t),change:function(t,r){e.prepend&&r.placeholder.index()<1?o("li.form-prepend").after(r.placeholder):e.append&&r.placeholder.index()>=A.children("li").length-1&&o("li.form-append").before(r.placeholder)},cancel:["input","select","textarea",".disabled-field",".form-elements",".btn","button",".is-locked"].join(", "),placeholder:"frmb-placeholder hoverDropStyleInverse"}),e.allowStageSort||A.sortable("disable"),C.sortable({helper:"clone",opacity:.9,connectWith:`#${l}, ${ft}`,cancel:".formbuilder-separator",cursor:"move",scroll:!1,placeholder:"hoverDropStyleInverse ui-state-highlight",tolerance:"pointer",start:(e,t)=>b.startMoving.call(b,e,t),stop:(e,t)=>{b.stopMoving.call(b,e,t)},revert:150,beforeStop:(e,t)=>b.beforeStop.call(b,e,t),distance:3,change:function(t,r){e.prepend&&r.placeholder.index()<1?o("li.form-prepend").after(r.placeholder):e.append&&r.placeholder.index()>=A.children("li").length-1&&o("li.form-append").before(r.placeholder)},update:function(t,r){if(b.doCancel)return!1;o(r.item).closest(".stage-wrap").length&&0===o(r.item).closest(ft).length?(b.doCancel=!0,L(r.item)):(T()&&he(),b.setFieldOrder(C),b.doCancel=!e.sortableControls)}}),C.on("mouseenter",(function(){b.stageIsEmpty()||A.children(pt+":not(:last-child)").addClass(ct)}));const L=t=>{if(t[0].classList.contains("input-set-control")){const r=[],o=e.inputSets.find((e=>B(e.name||e.label)===t[0].dataset.type));if(o&&o.showHeader){const e={type:"header",subtype:"h2",id:o.name,label:o.label};r.push(e)}r.push(...o.fields),r.forEach((e=>{W(e,!0),(b.stopIndex||0===b.stopIndex)&&b.stopIndex++}))}else W(t,!0)},I=o(d.editorWrap);o('<div class="snackbar">').appendTo(I);let P="cb-wrap",M="";if(e.stickyControls.enable){P+=" sticky-controls";const e={top:0,bottom:"auto",right:"auto",left:"auto"},t=Object.assign({},e,c.opts.stickyControls.offset);0!==t.top&&(M=`top: ${t.top}px`)}const F=g("div",d.controls,{id:`${s.formID}-cb-wrap`,className:P,style:M});e.showActionButtons&&F.appendChild(d.formActions);const U=g("div","",{id:`${s.formID}-gridModeHelp`,className:"grid-mode-help"});F.appendChild(U),I.append(d.stage,F),"textarea"!==t.type?o(t).append(I):o(t).replaceWith(I),o(d.controls).on("click","li.input-control, li.input-set-control",(({target:t})=>{b.stageIsEmpty()&&A.find(pt).eq(0).remove();const r=o(t).closest("li");b.stopIndex=e.append?A.children().length-1:void 0,L(r),b.save.call(b)}));const W=(t,r=!1)=>{let n={};if(t instanceof jQuery)if(n.type=t[0].dataset.type,n.type){const e=v.custom.lookup(n.type);if(e){const t=n.type;n=Object.assign({},e),n.label=v.custom.label(t)}else{const e=v.getClass(n.type);n.label=e.label(n.type)}}else{const e=t[0].attributes;r||(n.values=t.children().map(((e,t)=>({label:o(t).text(),value:o(t).attr("value"),selected:Boolean(o(t).attr("selected"))}))));for(let t=e.length-1;t>=0;t--)n[e[t].name]=e[t].value}else n=Object.assign({},t);n.name||(n.name=q(n)),r&&["text","number","file","date","select","textarea","autocomplete"].includes(n.type)&&(n.className=n.className||"form-control");const i=/(?:^|\s)btn-(.*?)(?:\s|$)/g.exec(n.className);if(i&&(n.style=i[1]),r){const e=setTimeout((()=>{document.dispatchEvent(w.fieldAdded),clearTimeout(e)}),10)}e.onAddField(s.lastID,n),me(n,r),e.onAddFieldAfter(s.lastID,n),d.stage.classList.remove("empty")};n.prepFieldVars=W;const $=function(t){(t=b.getData(t))&&t.length?(t.forEach((e=>V(e))),t.forEach((e=>W(D(e)))),d.stage.classList.remove("empty")):e.defaultFields&&e.defaultFields.length?(c.opts.defaultFields.forEach((e=>V(e))),b.addDefaultFields()):e.prepend||e.append||(d.stage.classList.add("empty"),d.stage.dataset.content=h().get("getStarted")),(()=>{const t=[],r=t=>g("li",e[t],{className:`disabled-field form-${t}`});return e.prepend&&!o(".disabled-field.form-prepend",d.stage).length&&(t.push(!0),A.prepend(r("prepend"))),e.append&&!o(".disabled-field.form-append",d.stage).length&&(t.push(!0),A.append(r("append"))),b.disabledTT(d.stage),t.some((e=>!0===e))})()&&d.stage.classList.remove("empty"),b.save()};function V(e){const t=b.getRowValue(e.className);t&&!f.includes(t)&&f.push(t)}const G=t=>{const{type:r}=t,o=[],n=v.getClass(r),i=(e=>{const t=["required","label","description","placeholder","className","name","access","value"],r=!["header","paragraph","file","autocomplete"].concat(d.optionFields).includes(e),o={autocomplete:t.concat(["options","requireValidOption"]),button:["label","subtype","style","className","name","value","access"],checkbox:["required","label","description","toggle","inline","className","name","access","other","options"],text:t.concat(["subtype","maxlength"]),date:t.concat(["subtype","min","max","step"]),file:t.concat(["multiple"]),header:["label","subtype","className","access"],hidden:["name","value","access"],paragraph:["label","subtype","className","access"],number:t.concat(["subtype","min","max","step"]),select:t.concat(["multiple","options"]),textarea:t.concat(["subtype","maxlength","rows"])};e in v.registeredSubtypes&&!(e in o)&&(o[e]=t.concat(["subtype"])),o["checkbox-group"]=o.checkbox,o["radio-group"]=o.checkbox;const n=o[e];return"radio-group"===e&&Y("toggle",n),["header","paragraph","button"].includes(e)&&Y("description",n),r||Y("value",n),n||t})(r),l={required:()=>pe(t),toggle:()=>re("toggle",t,{first:h().get("toggle")}),inline:()=>{const e={first:h().get("inline"),second:h().get("inlineDesc",r.replace("-group",""))};return re("inline",t,e)},label:()=>ue("label",t),description:()=>ue("description",t),subtype:e=>ce("subtype",t,x[r],e),style:()=>ie(t.style),placeholder:()=>ue("placeholder",t),rows:()=>de("rows",t),className:e=>ue("className",t,e),name:e=>ue("name",t,e),value:()=>ue("value",t),maxlength:()=>de("maxlength",t),access:()=>{const r=[`<div class="available-roles" ${t.role?'style="display:block"':""}>`];for(a in e.roles)if(e.roles.hasOwnProperty(a)){const t=`fld-${s.lastID}-roles-${a}`,o={type:"checkbox",name:"roles[]",value:a,id:t,className:"roles-field"};f.includes(a)&&(o.checked="checked"),r.push(`<label for="${t}">`),r.push(b.input(o).outerHTML),r.push(` ${e.roles[a]}</label>`)}r.push("</div>");const o={first:h().get("roles"),second:h().get("limitRole"),content:r.join("")};return re("access",t,o)},other:()=>re("other",t,{first:h().get("enableOther"),second:h().get("enableOtherMsg")}),options:()=>function(e){const{type:t,values:r}=e;let o;const n=[g("a",h().get("addOption"),{className:"add add-opt"})],i=[g("label",h().get("selectOptions"),{className:"false-label"})],l=e.multiple||"checkbox-group"===t,a=e=>{const t=h().get("optionCount",e);return{selected:!1,label:t,value:B(t)}};if(r&&r.length)o=r.map((e=>Object.assign({},{selected:!1},e)));else{let e=[1,2,3];["checkbox-group","checkbox"].includes(t)&&(e=[1]),o=e.map(a);const r=o[0];r.hasOwnProperty("selected")&&"radio-group"!==t&&(r.selected=!0)}const s=g("div",n,{className:"option-actions"}),d=g("ol",o.map(((r,o,n,i=e.name)=>{const a=c.opts.onAddOption(r,{type:t,index:o,isMultiple:l});return Ne(i+"-options",a,l)})),{className:"sortable-options"}),f=g("div",[d,s],{className:"sortable-options-wrap"});return i.push(f),g("div",i,{className:"form-group field-options"}).outerHTML}(t),requireValidOption:()=>re("requireValidOption",t,{first:" ",second:h().get("requireValidOption")}),multiple:()=>{const e={default:{first:"Multiple",second:"set multiple attribute"},file:{first:h().get("multipleFiles"),second:h().get("allowMultipleFiles")},select:{first:" ",second:h().get("selectionsMessage")}};return re("multiple",t,e[r]||e.default)}};let a;const f=void 0!==t.role?t.role.split(","):[];["min","max","step"].forEach((e=>{l[e]="number"===r?()=>de(e,t):()=>ue(e,t)}));const u=["name","className","subtype"],p=Object.assign({},e.typeUserAttrs["*"],e.typeUserAttrs[r]);if(Object.keys(i).forEach((t=>{const a=i[t],s=[!0],d=e.disabledAttrs.includes(a);if(e.typeUserDisabledAttrs[r]){const t=e.typeUserDisabledAttrs[r];s.push(!t.includes(a))}if(n.definition.hasOwnProperty("disabledAttrs")){const e=n.definition.disabledAttrs;s.push(!e.includes(a))}if(n.definition.hasOwnProperty("defaultAttrs")){const e=Object.keys(n.definition.defaultAttrs);s.push(!e.includes(a))}if(p){const e=Object.keys(p);s.push(!e.includes(a))}d&&!u.includes(a)&&s.push(!1),s.every(Boolean)&&o.push(l[a](d))})),n.definition.hasOwnProperty("defaultAttrs")){const e=K(n.definition.defaultAttrs,t);o.push(e)}if(p){const e=K(p,t);o.push(e)}return o.join("")};function X(e){return[["array",({options:e})=>!!e],["boolean",({type:e})=>"checkbox"===e],[typeof e.value,()=>!0]].find((t=>t[1](e)))[0]}function Z(e,t){return e.subtype&&e.subtype===t}function K(t,r){const o=[],n={array:ee,string:_,number:de,boolean:(e,t)=>{let o=!1;return"checkbox"===e.type?o=Boolean(!!t.hasOwnProperty("value")&&t.value):r.hasOwnProperty(e)?o=r[e]:(t.hasOwnProperty("value")||t.hasOwnProperty("checked"))&&(o=t.value||t.checked||!1),re(e,nt(nt({},t),{},{[e]:o}),{first:i[e]})}};for(const l in t)if(t.hasOwnProperty(l)){const a=X(t[l]);if("undefined"!==a){const e=h().get(l),s=Object.assign({},t[l]);let d=s.value;"boolean"===a?s[l]??=s.value:"number"===a?s[l]??=fe(r[l],d):(d??="",s[l]??=r[l]||d),s.value=s[l],s.label&&(i[l]=Array.isArray(s.label)?h().get(...s.label)||s.label[0]:s.label),n[a]&&o.push(n[a](l,s)),i[l]=e}else if("undefined"===a&&Z(r,l))o.push(K(t[l],r));else{const r={};r[l]=t[l],e.notify.warning("Warning: unable to process typeUserAttr definition : "+JSON.stringify(r))}}return o.join("")}function _(e,t){const{class:r,className:o}=t,n=rt(t,Ye);let l={id:e+"-"+s.lastID,title:n.description||n.label||e.toUpperCase(),name:e,type:n.type||"text",className:[`fld-${e}`,(r||o||"").trim()],value:n.hasOwnProperty(e)?n[e]:n.value||""};const a=`<label for="${l.id}">${i[e]||""}</label>`;return["checkbox","checkbox-group","radio-group"].includes(l.type)||l.className.push("form-control"),l=Object.assign({},n,l),`<div class="form-group ${e}-wrap">${a}<div class="input-wrap">${(()=>{if("textarea"===l.type){const e=l.value;return delete l.value,`<textarea ${R(l)}>${e}</textarea>`}return`<input ${R(l)}>`})()}</div></div>`}function ee(e,t){const{multiple:r,options:o,label:n,value:l,class:a,className:d}=t,c=rt(t,Ke),f=t.hasOwnProperty(e)?t[e]:l||[],u=Object.keys(o).map((e=>{const t={value:e},r=o[e],n=Array.isArray(r)?h().get(...r)||r[0]:r;return(Array.isArray(f)?f.includes(e):e===f)&&(t.selected=!0),g("option",n,t)})),p={id:`${e}-${s.lastID}`,title:c.description||n||e.toUpperCase(),name:e,className:`fld-${e} form-control ${a||d||""}`.trim()};r&&(p.multiple=!0);const m=`<label for="${p.id}">${i[e]}</label>`;return Object.keys(c).forEach((function(e){p[e]=c[e]})),`<div class="form-group ${e}-wrap">${m}<div class="input-wrap">${g("select",u,p).outerHTML}</div></div>`}const re=(e,t,r={})=>{const o=t=>g("label",t,{for:`${e}-${s.lastID}`}).outerHTML,n={type:"checkbox",className:`fld-${e}`,name:e,id:`${e}-${s.lastID}`};t[e]&&(n.checked=!0);const i=[];let l=[g("input",null,n).outerHTML];return r.first&&i.push(o(r.first)),r.second&&l.push(" ",o(r.second)),r.content&&l.push(r.content),l=g("div",l,{className:"input-wrap"}).outerHTML,g("div",i.concat(l),{className:`form-group ${e}-wrap`}).outerHTML},ie=e=>{let t="";"undefined"===e&&(e="default");const r=`<label>${i.style}</label>`;return t+=b.input({value:e||"default",type:"hidden",className:"btn-style"}).outerHTML,t+='<div class="btn-group" role="group">',ve.btn.forEach((r=>{const o=["btn-xs","btn",`btn-${r}`];e===r&&o.push("selected");const n=g("button",h().get(`styles.btn.${r}`),{value:r,type:"button",className:o.join(" ")}).outerHTML;t+=n})),t+="</div>",t=g("div",[r,t],{className:"form-group style-wrap"}),t.outerHTML},de=(e,t)=>{const{class:r,className:o}=t,n=rt(t,_e),i=Number.isNaN(n[e])?void 0:n[e],l=h().get(e)||e,a={type:"number",value:i,name:e,placeholder:h().get(`placeholder.${e}`),className:`fld-${e} form-control ${r||o||""}`.trim(),id:`${e}-${s.lastID}`},d=b.input(D(a)).outerHTML;return g("div",[`<label for="${a.id}">${l}</label>`,`<div class="input-wrap">${d}</div>`],{className:`form-group ${e}-wrap`}).outerHTML},ce=(e,t,r,o=!1)=>{const n=r.map(((r,o)=>{let n=Object.assign({label:`${i.option} ${o}`,value:void 0},r);return r.value===t[e]&&(n.selected=!0),n=D(n),g("option",n.label,n)})),l={id:e+"-"+s.lastID,name:e,className:`fld-${e} form-control`},a=h().get(e)||te(e)||"",d=g("label",a,{for:l.id}),c=g("select",n,l),f=g("div",c,{className:"input-wrap"});return g("div",[d,f],{className:`form-group ${l.name}-wrap`,style:"display: "+(o?"none":"block")}).outerHTML},ue=(t,r,o=!1)=>{let n=r[t]||"",i=h().get(t)||t;"label"===t&&(["paragraph"].includes(r.type)?i=h().get("content"):n=J(n));const l=h().get(`placeholders.${t}`)||"";let a="";if(![].some((e=>!0===e))){const d={name:t,placeholder:l,className:`fld-${t} form-control`,id:`${t}-${s.lastID}`},c=g("label",i,{for:d.id}).outerHTML;"label"!==t||e.disableHTMLLabels?"textarea"===r.type&&"value"===t?a+=g("textarea",n,d).outerHTML:(d.value=n,d.type="text",a+=`<input ${R(d)}>`):(d.contenteditable=!0,a+=g("div",n,d).outerHTML);const f=`<div class="input-wrap">${a}</div>`;let u=o?"none":"block";"value"===t&&(u=r.subtype&&"quill"===r.subtype&&"none"),a=g("div",[c,f],{className:`form-group ${t}-wrap`,style:`display: ${u}`})}return a.outerHTML},pe=e=>{const{type:t}=e,r=[];let o="";return["header","paragraph","button"].includes(t)&&r.push(!0),r.some((e=>!0===e))||(o=re("required",e,{first:h().get("required")})),o},me=function(t,r=!0){const l=Se(t);s.lastID=b.incrementId(s.lastID);const a=t.type||"text";let c=t.label||(r?i[a]||h().get("label"):"");var f;"hidden"!==a&&""!==c||(c=`${null!==(f=h().get(a))&&void 0!==f?f:a}: ${t.name}`);const u=e.disabledFieldButtons[a]||t.disabledFieldButtons;let p=[g("a",null,{type:"remove",id:"del_"+s.lastID,className:`del-button btn ${lt}cancel delete-confirm`,title:h().get("removeMessage")}),g("a",null,{type:"edit",id:s.lastID+"-edit",className:`toggle-form btn ${lt}pencil`,title:h().get("hide")}),g("a",null,{type:"copy",id:s.lastID+"-copy",className:`copy-button btn ${lt}copy`,title:h().get("copyButtonTooltip")})];T()?p.push(g("a",null,{type:"grid",id:s.lastID+"-grid",className:`grid-button btn ${lt}grid`,title:"Grid Mode"})):p.push(g("a",null,{type:"sort",id:s.lastID+"-sort-higher",className:`sort-button sort-button-higher btn ${lt}sort-higher`,title:"Move Higher"}),g("a",null,{type:"sort",id:s.lastID+"-sort-lower",className:`sort-button sort-button-lower btn ${lt}sort-lower`,title:"Move Lower"})),u&&Array.isArray(u)&&(p=p.filter((e=>!u.includes(e.type))));const m=[g("div",p,{className:"field-actions"})],w=e.disableHTMLLabels?document.createTextNode(c):J(c);m.push(g("label",w,{className:"field-label"})),m.push(g("span"," *",{className:"required-asterisk",style:t.required?"display:inline":""}));const y={className:"tooltip-element",tooltip:t.description,style:t.description?"display:inline-block":"display:none"};m.push(g("span","?",y));const v=g("div","",{className:"prev-holder",dataFieldId:s.lastID});m.push(v);const x=g("div",[G(t),g("a",h().get("close"),{className:"close-field"})],{className:"form-elements"}),C=g("div",x,{id:`${s.lastID}-holder`,className:"frm-holder",dataFieldId:s.lastID});n.currentEditPanel=C,m.push(C);const O=g("li",m,{class:`${a}-field form-field`,type:a,id:s.lastID}),E=o(O);let D;if(be(E),E.data("fieldData",{attrs:t}),void 0!==b.stopIndex?o(d.stage).children().eq(b.stopIndex).before(E):A.append(E),o(".sortable-options",E).sortable({update:()=>b.updatePreview(E)}),b.updatePreview(E),T()){const t=`div.row-${l.rowUniqueId}`;let r=!1;A.children(t).length?D=A.children(t):(D=g("div",null,{id:`${O.id}-row`,className:`row row-${l.rowUniqueId} ${at}`}),r=!0),j&&S&&(k.attr("id",D.id),k.attr("class",D.className),k.attr("style",""),k.attr("data-row-id",l.rowUniqueId),D=k);const n=g("div",null,{id:`${O.id}-cont`,className:`${l.columnSize} ${st}`});j&&N&&("true"===k.attr("prepend")?o(n).prependTo(D):o(n).insertAfter(`#${k.attr("appendAfter")}`)),N||o(n).appendTo(D),!j&&r&&E.after(D),E.appendTo(n),r&&(xe(D),he(),we(D),e.enableColumnInsertMenu&&(o(D).off("mouseenter"),o(D).on("mouseenter",(function(e){ke(o(e.currentTarget))})),o(D).off("mouseleave"),o(D).on("mouseleave",(function(e){je(o(e.currentTarget))})))),ke(D,!0),l.addedDefaultColumnClass&&E.attr("addedDefaultColumnClass",!0),b.tmpCleanPrevHolder(o(v))}e.typeUserEvents[a]&&e.typeUserEvents[a].onadd?e.typeUserEvents[a].onadd(O):e.typeUserEvents["*"]&&e.typeUserEvents["*"].onadd&&e.typeUserEvents["*"].onadd(O),r&&(e.editOnAdd&&(b.closeAllEdit(),b.toggleEdit(s.lastID,!1)),O.scrollIntoView&&e.scrollToFieldOnAdd&&O.scrollIntoView({behavior:"smooth"})),T()&&j&&N&&Be(D,!0),j=!1,S=!1,N=!1};function be(e){T()&&e.mouseenter((function(e){Pe||(Le=o(this),Re=e.pageX,Ie=e.pageY)}))}function he(){A.find(pt+":not(:last-child)").css("height","1px").addClass(ct)}function we(e){const t=o(e).clone();if(t.addClass(ct).addClass(dt).html(""),t.css("height","1px"),t.attr("class",t.attr("class").replace("row-","")),t.removeAttr("id"),0===o(e).index()){const e=o(t).clone();A.prepend(e),xe(e)}t.insertAfter(o(e)),xe(t),A.find(ft+":last-of-type").removeClass(ct)}function ye(){A.children(pt).remove(),A.children(ft).each(((e,t)=>{we(o(t))})),A.find(ft+":last-of-type").removeClass(ct)}function xe(e){if(!T())return;o(e).sortable({connectWith:[ft],cursor:"move",opacity:.9,revert:150,distance:3,tolerance:"pointer",helper:function(e,t){const r=t.clone();return r.find(".field-actions").remove(),r.css({width:"20%",height:"100px",minHeight:"60px",overflow:"hidden"}),r},over:function(e){const t=o(e.target),r=t.hasClass(dt);r||Ee(t),t.addClass("hoverDropStyleInverse"),r||(he(),t.prev(pt).removeClass(ct).css("height","40px"),t.next(pt).removeClass(ct).css("height","40px"))},out:function(e){A.children(pt).removeClass("hoverDropStyleInverse"),o(e.target).removeClass("hoverDropStyleInverse")},placeholder:"hoverDropStyleInverse",receive:function(e,t){const r=o(t.sender).attr("id")===C.attr("id"),n=o(t.item).parent().hasClass(dt),i=!n&&o(t.item).parent().hasClass(at);if(n)if(r)S=!0,j=!0,k=o(t.item).parent();else{const e=o(t.item),r=Se({}),n=g("div",null,{id:`${e.find("li").attr("id")}-row`,className:`row row-${r.rowUniqueId} ${at}`});o(t.item).parent().replaceWith(n),be(o(t.item)),e.appendTo(n),xe(n),b.syncFieldWithNewRow(e[0],e.closest(ft)[0])}if(i&&r){k=o(t.item).prev().hasClass("btnAddControl")?o(t.item).prev():o(t.item).next().hasClass("btnAddControl")?o(t.item).next():o(t.item).attr("prepend","true");const e=b.getRowClass(o(t.item).parent().attr("class"));k.addClass(e),N=!0,j=!0,b.stopIndex=void 0}j&&(b.doCancel=!0,L(t.item),b.save.call(b)),Fe(),ye();const l=o(t.item).find("li");l.length&&(Ce(l),Oe(l),b.tmpCleanPrevHolder(o(t.item).find(".prev-holder")))},start:(e,t)=>{A.addClass("__preventColButtons"),Ee(t.item.closest(ft))},stop:(e,t)=>{A.removeClass("__preventColButtons"),A.children(pt).removeClass("hoverDropStyleInverse"),Fe(),Be(t.item.closest(ft),!0)},update:(e,t)=>{b.syncFieldWithNewRow(t.item,o(t.item).closest(ft)[0])}});const t=b.getRowValue(e.className);"0"!==t&&o(e).attr("data-row-id",t)}function Ce(e){const t=e.find('textarea[type="tinymce"]');t.length&&(window.lastFormBuilderCopiedTinyMCE=window.tinymce.get(t.attr("id")).save())}function Oe(e){b.updatePreview(e),b.save.call(b)}function ke(t,r=!1){if(!e.enableColumnInsertMenu||A.hasClass("__preventColButtons"))return;o(t).children("button.btnAddControl").remove();const n=o(t).children(ut);n.each(((e,t)=>{const i=o(t);i.addClass("colWithInsertButtons"),0===n.index(i)&&o(`<button type="button" class="formbuilder-icon-plus btnAddControl ${b.getRowClass(i.parent().attr("class"))}" prepend="true" style='visibility: ${r?"hidden":"visible"}'></button>`).insertBefore(i),o(`<button type="button" class="formbuilder-icon-plus btnAddControl ${b.getRowClass(i.parent().attr("class"))}" appendAfter="${i.attr("id")}" style='visibility: ${r?"hidden":"visible"}'></button>`).insertAfter(i)}))}function Ee(e){e.find("button.btnAddControl").remove(),e.find(ut).removeClass("colWithInsertButtons")}function je(e){e.find("button.btnAddControl").css("visibility","hidden")}function Se(t){if(!T())return{};const r=b.tryParseColumnInfo(t);if(!r.rowUniqueId){if(j&&N)r.rowUniqueId=b.getRowValue(k.attr("class"));else{let e;if(0===f.length)e=1;else{const t=f.filter((e=>!isNaN(e)&&!isNaN(parseInt(e)))).map((e=>parseInt(e)));e=Math.max(...t,0)+1}r.rowUniqueId=e.toString()}r.columnSize=e.defaultGridColumnClass,t.className||(t.className=""),t.className+=` row-${r.rowUniqueId} ${r.columnSize}`,r.addedDefaultColumnClass=!0}return f.includes(r.rowUniqueId)||f.push(r.rowUniqueId),r}const Ne=function(e,t,r){const o={selected:r?"checkbox":"radio"},n={boolean:(t,r)=>{const n={value:t,type:o[r]||"checkbox"};return t&&(n.checked=!!t),n.name=e,["input",null,n]},number:e=>["input",null,{value:e,type:"number"}],string:(e,t)=>["input",null,{value:e,type:"text",placeholder:h().get(`placeholder.${t}`)||""}],array:e=>["select",e.map((({label:e,value:t})=>g("option",e,{value:t})))],object:e=>{let{tag:t,content:r}=e;return[t,r,rt(e,et)]}};t=nt(nt({},{selected:!1,label:"",value:""}),t);const i=Object.entries(t).map((([e,t])=>{const r=H(t),[o,i,l]=n[r](t,e),a=`option-${e} option-attr`;return l["data-attr"]=e,l.className=l.className?`${l.className} ${a}`:a,g(o,i,l)})),l={className:`remove btn ${lt}cancel`,title:h().get("removeMessage")};return i.push(g("a",null,l)),g("li",i).outerHTML},De=[".form-elements input",".form-elements select",".form-elements textarea"].join(", ");A.on("change blur keyup click",De,r()((e=>{if(e){if([({type:e,target:t})=>"keyup"===e&&"className"===t.name].some((t=>t(e))))return!1;Oe(o(e.target).closest(".form-field"))}}),333,{leading:!1})),A.on("click touchstart",".remove",(t=>{const r=o(t.target).parents(".form-field:eq(0)"),n=r[0],i=n.getAttribute("type"),l=o(t.target.parentElement);t.preventDefault(),n.querySelector(".sortable-options").childNodes.length<=2&&!i.includes("checkbox")?e.notify.error("Error: "+h().get("minOptionMessage")):l.slideUp("250",(()=>{l.remove(),Oe(r)}))})),A.on("touchstart","input",(e=>{const t=o(e.target);if(!0===e.handled)return!1;if("checkbox"===t.attr("type"))t.trigger("click");else{t.focus();const e=t.val();t.val(e)}})),A.on("click touchstart",".toggle-form, .close-field",(function(e){if(e.stopPropagation(),e.preventDefault(),!0===e.handled)return!1;{const t=o(e.target).parents(".form-field:eq(0)").attr("id");b.toggleEdit(t),e.handled=!0}})),A.on("dblclick","li.form-field",(e=>{if(!["select","input","label","textarea"].includes(e.target.tagName.toLowerCase())&&!0!==e.target.isContentEditable&&(e.stopPropagation(),e.preventDefault(),!0!==e.handled)){const t=o(e.target).closest("li.form-field").attr("id");b.toggleEdit(t),e.handled=!0}})),A.on("change",'[name="subtype"]',(e=>{const t=o(e.target).closest("li.form-field");o(".value-wrap",t).toggle("quill"!==e.target.value)})),A.on("change",'[name="name"]',(t=>{const r=t.target.value;(e=>{const t=document,r=document.createElement("form");return e in t||e in r})(r)&&e.notify.error("Potential for Dom Clobbering with field name "+r)})),A.on("change",[".prev-holder input",".prev-holder select",".prev-holder textarea"].join(", "),(e=>{let t;if(e.target.classList.contains("other-option"))return;const r=ne(e.target,".form-field");if(["select","checkbox-group","radio-group"].includes(r.type)){const o=r.getElementsByClassName("option-value");"select"===r.type?Q(o,(t=>{o[t].parentElement.childNodes[0].checked=e.target.value===o[t].value})):(t=document.getElementsByName(e.target.name),Q(t,(e=>{t[e].classList.contains("other-option")||(o[e].parentElement.childNodes[0].checked=t[e].checked)})))}else{const t=document.getElementById("value-"+r.id);t&&(t.value=e.target.value)}b.save.call(b)})),oe(d.stage,"keyup change",(({target:e})=>{if(!e.classList.contains("fld-label"))return;const t=e.value||e.innerHTML,r=ne(e,".form-field").querySelector(".field-label");O(r,J(t),c.opts.disableHTMLLabels)})),A.on("keyup","input.error",(({target:e})=>o(e).removeClass("error"))),A.on("keyup",'input[name="description"]',(function(e){const t=o(e.target).parents(".form-field:eq(0)"),r=o(".tooltip-element",t),n=o(e.target).val();if(""!==n)if(r.length)r.attr("tooltip",n).css("display","inline-block");else{const e=`<span class="tooltip-element" tooltip="${n}">?</span>`;o(".field-label",t).after(e)}else r.length&&r.css("display","none")})),A.on("change",".fld-multiple",(e=>{const t=e.target.checked?"checkbox":"radio",r=o(".option-selected",o(e.target).closest(".form-elements"));return r.each((e=>r[e].type=t)),t})),A.on("blur","input.fld-name",(function(e){e.target.value=le(e.target.value),""===e.target.value?o(e.target).addClass("field-error").attr("placeholder",h().get("cannotBeEmpty")):o(e.target).removeClass("field-error")})),A.on("blur","input.fld-maxlength, input.fld-rows",(e=>{e.target.value=ae(e.target.value)})),A.on("click touchstart",".btnAddControl",(function(e){const t=o(e.currentTarget);E=C.clone(),E.hover((function(){}),(function(){E.remove()})),E.on("click","li",(({target:e})=>{N=!0,j=!0,k=t;const r=o(e).closest("li");b.stopIndex=void 0,L(r),b.save.call(b),E.remove()})),A.append(E),0==t.index()?E.css({position:"fixed",left:t.offset().left,top:t.offset().top-o(window).scrollTop()}):E.css({position:"fixed",left:t.offset().left-80,top:t.offset().top-o(window).scrollTop()});const r=E.offset().top+E.outerHeight(),n=o(window).scrollTop()+o(window).innerHeight();r>n&&E.css({top:parseInt(E.css("top"))-(r-n)})})),A.on("click",`.${lt}copy`,(function(t){t.preventDefault();const r=o(t.target).parent().parent("li"),n=function(t){s.lastID=b.incrementId(s.lastID),Ce(t);const r=t.attr("id"),n=t.attr("type"),i=n+"-"+(new Date).getTime(),l=t.clone();return o(".fld-name",l).val(i),l.find("[id]").each(((e,t)=>{t.id=t.id.replace(r,s.lastID)})),l.find("[for]").each(((e,t)=>{const o=t.getAttribute("for").replace(r,s.lastID);t.setAttribute("for",o)})),t.find("select").each((function(e){l.find("select").eq(e).val(o(this).val())})),l.attr("id",s.lastID),l.attr("name",i),l.addClass("cloned"),o(".sortable-options",l).sortable(),e.typeUserEvents[n]&&e.typeUserEvents[n].onclone?e.typeUserEvents[n].onclone(l[0]):e.typeUserEvents["*"]&&e.typeUserEvents["*"].onclone&&e.typeUserEvents["*"].onclone(l[0]),l}(r);T()?function(e,t){const r=o(`#className-${t.attr("id")}`),n=Se({}),i=g("div",null,{id:`${e.attr("id")}-row`,className:`row row-${n.rowUniqueId} ${at}`}),l=g("div",null,{id:`${e.attr("id")}-cont`,className:`${b.getBootstrapColumnClass(r.val())} ${st}`});let a;o(l).appendTo(i),t.parent().is("div")?a=t.closest(ft):t.parent().is("ul")&&(a=t),o(i).insertAfter(a),e.appendTo(l),xe(i),ye(),b.syncFieldWithNewRow(e[0],e.closest(ft)[0])}(n,r):n.insertAfter(r),Oe(n),b.tmpCleanPrevHolder(n.find(".prev-holder")),e.editOnAdd&&b.closeField(s.lastID,!1)})),T()&&A.on("stageEmptied",(()=>{f=[]})),A.on("click",".delete-confirm",(t=>{t.preventDefault();const r=t.target.getBoundingClientRect(),n=document.body.getBoundingClientRect(),i={pageX:r.left+r.width/2,pageY:r.top-n.top-12},l=o(t.target).parents(".form-field:eq(0)").attr("id"),a=o(document.getElementById(l));if(document.addEventListener("modalClosed",(function(){a.removeClass("deleting")}),!1),e.fieldRemoveWarn){const e=g("h3",h().get("warning")),t=g("p",h().get("fieldRemoveWarning"));b.confirm([e,t],(()=>b.removeField(l)),i),a.addClass("deleting")}else b.removeField(l)}));let Le,Re,Ie,Pe=!1,Me=0;function Be(e,t=!1){const r=e.children(`div${ut}`).length,n=Math.floor(12/r);e.children(`div${ut}`).each(((e,r)=>{const i=o(`#${r.id}`);t||"true"!=i.find("li").attr("manuallyChangedDefaultColumnClass")?b.syncBootstrapColumnWrapperAndClassProperty(r.id.replace("-cont",""),n):b.showToast(`Preserving column size of field ${e+1} because you had personally adjusted it`,4e3)}))}function Fe(){A.find(ut).each(((e,t)=>{const r=o(t);r.is(":empty")&&!n.preserveTempContainers.includes(r.attr("id"))&&r.remove()})),A.children(ft).not(pt).each(((e,t)=>{if(0===o(t).children(ut).length){const e=b.getRowValue(o(t).attr("class"));f=f.filter((t=>t!==e)),o(t).remove()}else ke(o(t),!0)}))}function qe(e=!0){if(e)Pe=!0,b.showToast("Starting Grid Mode - Use the mousewheel to resize.",1500),C.css("display","none"),o(d.formActions).css("display","none"),o(U).html("\n    <div style='padding:5px'>\n      <h3 class=\"text text-center\">Grid Mode</h3>    \n      \n      <table style='border-spacing:7px;border-collapse: separate'>\n        <thead>\n          <tr>\n            <th>Action</th>\n            <th>Result</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr>\n            <td><kbd>MOUSEWHEEL</kbd></td>\n            <td>Adjust the field column size</td>\n          </tr>    \n          <tr>\n            <td><kbd>W or &#x2191;</kbd></td> \n            <td>Move entire row up</td>\n          </tr>\n          <tr>\n              <td><kbd>S or &#x2193;</kbd></td> \n              <td>Move entire row down</td>\n          </tr>\n          <tr>\n              <td><kbd>A or &#x2190;</kbd></td>\n              <td>Move field left within the row</td>\n          </tr>\n          <tr>\n              <td><kbd>D or &#x2192;</kbd></td> \n              <td>Move field right within the row</td>\n          </tr>\n          <tr>\n            <td><kbd>R</kbd></td> \n            <td>Resize all fields within the row to be maximally equal</td>\n          </tr>\n          <tr>\n        </tbody> \n      </table>\n\n      <h5 class=\"text text-center\" style='padding-top:10px'>Current Row Fields</h5>    \n      \n      <table class='gridHelpCurrentRow'>\n        <colgroup>\n          <col width=\"100%\" />\n          <col width=\"0%\" />\n        </colgroup>\n        \n        <thead>\n          <tr>\n            <th>Field</th>\n            <th>Size</th>\n          </tr>\n        </thead>\n\n        <tbody>\n        </tbody> \n      </table>\n      \n    </div>\n    "),He(),b.closeAllEdit(),b.toggleHighlight(Le),he();else{b.showToast("Grid Mode Finished",1500);const e=Le.closest(ft);let t=0;e.children(`div${ut}`).each(((e,r)=>{const n=o(`#${r.id}`).find("li").attr("id");t+=b.getBootstrapColumnValue(o(`#${n}-cont`).attr("class"))})),t>12&&Be(e,!0),Pe=!1,Le=null,o(U).empty(),C.css("display","unset"),o(d.formActions).css("display","unset")}}function He(){o(U).find(".gridHelpCurrentRow tbody").empty(),Le.closest(ft).children(`div${ut}`).each(((e,t)=>{const r=o(`#${t.id}`).find("li").attr("id"),n=o(`#${r}`),i=n.attr("type");let l=o(`#label-${r}`).html();"hidden"!==i&&"paragraph"!==i||(l=o(`#name-${r}`).val()),l||(l=n.attr("id"));let a="";Le.attr("id")===r&&(a="currentGridModeFieldHighlight"),o(U).find(".gridHelpCurrentRow tbody").append(`\n        <tr>\n          <td class='grid-mode-help-row1 ${a}'>${l}</td>\n          <td class='grid-mode-help-row2 ${a}'>\n            ${b.getBootstrapColumnValue(o(`#${r}-cont`).attr("class"))}\n          </td>\n        <tr>\n      `)}))}if(A.on("click touchstart",".grid-button",(e=>{e.preventDefault();const t=o(e.target).parents(".form-field:eq(0)").attr("id");Le=o(document.getElementById(t)),Re=e.pageX,Ie=e.pageY,Me=0,qe()})),A.on("wheel",(function(e){if(0!==e.originalEvent.deltaY&&Pe){e.preventDefault(),Me+=e.originalEvent.deltaY;const t=120;if(Me>0&&Me<t||Me<0&&Me>-t)return;const r=Le.closest("div"),n=b.getBootstrapColumnValue(r.attr("class")),i=Math.round(Me/t);Me%=t;const l=n+i;if(l>12)return void b.showToast('<b class="formbuilder-required">Column Size cannot exceed 12</b>');if(l<1)return void b.showToast('<b class="formbuilder-required">Column Size cannot be less than 1</b>');const a=Le.closest(ft);let s=l;if(a.children(`div${ut}`).each(((e,t)=>{const r=o(`#${t.id}`).find("li").attr("id");r!=Le.attr("id")&&(s+=b.getBootstrapColumnValue(o(`#${r}-cont`).attr("class")))})),s>12)return void b.showToast('<b class="formbuilder-required">There is a maximum of 12 columns per row</b>');b.syncBootstrapColumnWrapperAndClassProperty(Le.attr("id"),l),Le.attr("manuallyChangedDefaultColumnClass",!0),He(),b.toggleHighlight(Le)}})),o(document).keydown((e=>{if(Pe){e.preventDefault();const t=Le.closest(ft);switch(event.code){case"KeyW":case"ArrowUp":Ee(t),function(e){const t=e.prevAll().not(pt).not(".form-prepend").first();t.length&&(o(Le.parent().parent()).swapWith(t),b.toggleHighlight(Le))}(t);break;case"KeyS":case"ArrowDown":Ee(t),function(e){const t=e.nextAll().not(mt).not(".form-append").first();t.length&&(o(Le.parent().parent()).swapWith(t),b.toggleHighlight(Le))}(t);break;case"KeyA":case"ArrowLeft":Ee(t),function(){const e=Le.parent().prev();e.length&&Le.parent().after(e),b.toggleHighlight(Le)}();break;case"KeyD":case"ArrowRight":Ee(t),function(){const e=Le.parent().next();e.length&&Le.parent().before(e),b.toggleHighlight(Le)}();break;case"KeyR":Ee(t),Be(t,!0),ke(t,!0)}He(),je(t)}})),o(document).mousemove((e=>{Pe&&b.getDistanceBetweenPoints(Re,Ie,e.pageX,e.pageY)>c.opts.cancelGridModeDistance&&qe(!1)})),o(document).on("checkRowCleanup",((e,t)=>{Fe();const r=o(`#${t.rowWrapperID}`);r.length&&Be(r,!0)})),o(document).on("fieldOpened",((e,t)=>{const r=o(`#${t.rowWrapperID}`);r.length&&je(r)})),A.on("click",".field-actions .sort-button",(function(e){e.preventDefault();const t=o(e.target).parent().parent("li");let r;o(e.target).hasClass("sort-button-higher")?(r=t.prev("li"),r.length&&!r.hasClass("form-prepend")&&t.insertBefore(r)):(r=t.next("li"),r.length&&!r.hasClass("form-append")&&t.insertAfter(r)),b.toggleHighlight(t)})),A.on("click",".style-wrap button",(e=>{const t=o(e.target),r=t.closest(".form-elements"),n=t.val(),i=o(".btn-style",r);i.val(n),t.siblings(".btn").removeClass("selected"),t.addClass("selected"),Oe(i.closest(".form-field"))})),A.on("click",".fld-required",(e=>{o(e.target).closest(".form-field").find(".required-asterisk").toggle()})),A.on("click","input.fld-access",(function(e){const t=o(e.target).closest(".form-field").find(".available-roles"),r=o(e.target);t.slideToggle(250,(function(){r.is(":checked")||o("input[type=checkbox]",t).removeAttr("checked")}))})),A.on("click",".add-opt",(function(e){e.preventDefault();const t=o(e.target).closest(".form-field").attr("type"),r=o(e.target).closest(".field-options"),n=o('[name="multiple"]',r),i=o(".option-selected:eq(0)",r),l=n.length?n.prop("checked"):"checkbox"===i.attr("type"),a=o(".sortable-options",r),s=c.opts.onAddOption({selected:!1,label:"",value:""},{type:t,index:a.children().length,isMultiple:l});a.append(Ne(i.attr("name"),s,l))})),A.on("mouseover mouseout",".remove, .del-button",(e=>o(e.target).closest("li").toggleClass("delete"))),$(),!0===e.disableInjectedStyle){const e=document.getElementsByClassName("formBuilder-injected-style");Q(e,(t=>a(e[t])))}else"bootstrap"===e.disableInjectedStyle&&d.editorWrap.classList.remove("formbuilder-embedded-bootstrap");return document.dispatchEvent(w.loaded),n.actions={getFieldTypes:t=>t?se(v.getRegistered(),e.disableFields):v.getRegistered(),clearFields:()=>b.removeAllFields(d.stage),showData:b.showData.bind(b),save:e=>{const t=b.save(e),r=window.JSON.parse(t);return c.opts.onSave(r),r},addField:(e,t)=>{b.stopIndex=s.formData.length?t:void 0,W(e)},removeField:b.removeField.bind(b),getData:b.getFormData.bind(b),setData:e=>{b.stopIndex=void 0,b.removeAllFields(d.stage),$(e)},setLang:e=>h().setCurrent.call(h(),e).then((()=>{d.stage.dataset.content=h().get("getStarted"),v.init(),d.empty(d.formActions),b.formActionButtons().forEach((e=>d.formActions.appendChild(e)))})),showDialog:b.dialog.bind(b),toggleFieldEdit:e=>{(Array.isArray(e)?e:[e]).forEach((e=>{["number","string"].includes(typeof e)&&("number"==typeof e?e=d.stage.children[e].id:/^frmb-/.test(e)||(e=d.stage.querySelector(e).id),b.toggleEdit(e))}))},toggleAllFieldEdit:()=>{Q(d.stage.children,(e=>{b.toggleEdit(d.stage.children[e].id)}))},closeAllFieldEdit:b.closeAllEdit.bind(b),getCurrentFieldId:()=>s.lastID},d.onRender(d.controls,(()=>{const e=setTimeout((()=>{d.stage.style.minHeight=`${d.controls.clientHeight}px`,clearTimeout(e)}),0)})),n}const gt=function(e,t){const r=this,o=jQuery.extend({},ye,e,!0),{i18n:n}=o,i=rt(o,tt);this.i18nOpts=jQuery.extend({},xe,n,!0);const l=()=>{console.error("formBuilder is still initialising"),console.info("See https://formbuilder.online/docs/formBuilder/actions/getData/#wont-work and https://formbuilder.online/docs/formBuilder/promise/ for more information on formBuilder asynchronous loading")};this.instance={actions:["getFieldTypes","addField","clearFields","closeAllFieldEdit","getData","removeField","save","setData","setLang","showData","showDialog","toggleAllFieldEdit","toggleFieldEdit","getCurrentFieldId"].reduce(((e,t)=>(e[t]=l,e)),{}),markup:z,get formData(){return r.instance.actions.getData!==l&&r.instance.actions.getData("json")},promise:new Promise((function(e,o){h().init(r.i18nOpts).then((()=>{const o=new ht(i,t[0],jQuery);jQuery(t[0]).data("formBuilder",o),Object.assign(r.instance,o.actions),r.instance.actions=o.actions,delete r.instance.promise,e(r.instance)})).catch((e=>{o(e),i.notify.error(e)}))}))}};jQuery.fn.formBuilder=function(e={},...t){if("string"!=typeof e){const t=new gt(e,this);return this.data("fbInstance",t.instance),t.instance}{const r=this.data("fbInstance");if(r[e])return"function"==typeof r[e]?r[e].apply(this,t):r[e]}}}()}()}(jQuery);