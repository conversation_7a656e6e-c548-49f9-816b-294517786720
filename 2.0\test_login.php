<?php

// Test database connection
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=mrma_gg', 'root', '');
    echo "Database connection: SUCCESS\n";

    // Check if super admin user exists
    $stmt = $pdo->prepare("SELECT id, name, email, role FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        echo "Super admin user found:\n";
        echo "ID: " . $user['id'] . "\n";
        echo "Name: " . $user['name'] . "\n";
        echo "Email: " . $user['email'] . "\n";
        echo "Role: " . $user['role'] . "\n";
    } else {
        echo "Super admin user NOT found\n";
    }

    // Check all users
    $stmt = $pdo->query("SELECT id, name, email, role FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "\nAll users in database:\n";
    foreach ($users as $u) {
        echo "ID: " . $u['id'] . ", Name: " . $u['name'] . ", Email: " . $u['email'] . ", Role: " . $u['role'] . "\n";
    }

} catch (PDOException $e) {
    echo "Database connection: FAILED - " . $e->getMessage() . "\n";
}
