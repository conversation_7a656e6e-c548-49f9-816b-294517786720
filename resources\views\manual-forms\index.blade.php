@extends('layouts.app')

@section('title', 'Manual Form Data Entry')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Manual Form Data Entry</h4>
                    <p class="text-muted">Use this interface to manually add form data when the regular form editing doesn't work.</p>
                </div>
                <div class="card-body">
                    <!-- Form Selection -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="formSelect" class="form-label">Select Form</label>
                            <select id="formSelect" class="form-select">
                                <option value="">Choose a form...</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="userSelect" class="form-label">Select User (Optional)</label>
                            <select id="userSelect" class="form-select">
                                <option value="">Use current user</option>
                            </select>
                        </div>
                    </div>

                    <!-- Form Structure Display -->
                    <div id="formStructureContainer" style="display: none;">
                        <h5>Form Fields</h5>
                        <div id="formFields" class="row">
                            <!-- Dynamic form fields will be inserted here -->
                        </div>

                        <div class="mt-4">
                            <button type="button" id="submitFormData" class="btn btn-primary">
                                <i class="fas fa-save"></i> Add Form Data
                            </button>
                            <button type="button" id="clearForm" class="btn btn-secondary">
                                <i class="fas fa-eraser"></i> Clear Form
                            </button>
                        </div>
                    </div>

                    <!-- Bulk Upload Section -->
                    <div class="mt-5">
                        <h5>Bulk Data Upload</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <label for="bulkDataTextarea" class="form-label">JSON Data (Array of submissions)</label>
                                <textarea id="bulkDataTextarea" class="form-control" rows="10"
                                    placeholder='[
  {
    "user_id": 1,
    "form_data": {
      "field1": "value1",
      "field2": "value2"
    }
  },
  {
    "user_id": 2,
    "form_data": {
      "field1": "value3",
      "field2": "value4"
    }
  }
]'></textarea>
                                <small class="text-muted">Enter JSON array of submissions. Each submission should have user_id and form_data.</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="button" id="bulkUpload" class="btn btn-warning">
                                <i class="fas fa-upload"></i> Bulk Upload
                            </button>
                        </div>
                    </div>

                    <!-- Recent Submissions -->
                    <div class="mt-5">
                        <h5>Recent Submissions</h5>
                        <div id="recentSubmissions">
                            <p class="text-muted">Select a form to view recent submissions.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Modal -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resultModalTitle">Result</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resultModalBody">
                <!-- Result content will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentFormStructure = null;
let currentFormId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadForms();
    setupEventListeners();
});

function loadForms() {
    fetch('/manual-forms/')
        .then(response => response.json())
        .then(data => {
            const formSelect = document.getElementById('formSelect');
            formSelect.innerHTML = '<option value="">Choose a form...</option>';

            data.forms.forEach(form => {
                const option = document.createElement('option');
                option.value = form.id;
                option.textContent = `${form.form_name} (${form.target_user})`;
                formSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading forms:', error);
            showResult('Error', 'Failed to load forms', 'error');
        });
}

function setupEventListeners() {
    document.getElementById('formSelect').addEventListener('change', function() {
        const formId = this.value;
        if (formId) {
            loadFormStructure(formId);
            loadRecentSubmissions(formId);
        } else {
            document.getElementById('formStructureContainer').style.display = 'none';
            document.getElementById('recentSubmissions').innerHTML = '<p class="text-muted">Select a form to view recent submissions.</p>';
        }
    });

    document.getElementById('submitFormData').addEventListener('click', submitFormData);
    document.getElementById('clearForm').addEventListener('click', clearForm);
    document.getElementById('bulkUpload').addEventListener('click', bulkUpload);
}

function loadFormStructure(formId) {
    currentFormId = formId;

    fetch(`/manual-forms/${formId}/structure`)
        .then(response => response.json())
        .then(data => {
            currentFormStructure = data.structure;
            renderFormFields(data.structure);
            document.getElementById('formStructureContainer').style.display = 'block';
        })
        .catch(error => {
            console.error('Error loading form structure:', error);
            showResult('Error', 'Failed to load form structure', 'error');
        });
}

function renderFormFields(structure) {
    const container = document.getElementById('formFields');
    container.innerHTML = '';

    if (!Array.isArray(structure)) {
        container.innerHTML = '<div class="col-12"><p class="text-danger">Invalid form structure</p></div>';
        return;
    }

    structure.forEach((field, index) => {
        const fieldHtml = createFieldHtml(field, index);
        container.insertAdjacentHTML('beforeend', fieldHtml);
    });
}

function createFieldHtml(field, index) {
    const fieldId = `field_${index}`;
    const required = field.required ? 'required' : '';
    const requiredMark = field.required ? ' *' : '';

    switch(field.type) {
        case 'text':
        case 'email':
            return `
                <div class="col-md-6 mb-3">
                    <label for="${fieldId}" class="form-label">${field.label}${requiredMark}</label>
                    <input type="${field.type}" class="form-control" id="${fieldId}" name="${field.label}"
                           placeholder="${field.placeholder || ''}" ${required}>
                </div>
            `;
        case 'textarea':
            return `
                <div class="col-md-12 mb-3">
                    <label for="${fieldId}" class="form-label">${field.label}${requiredMark}</label>
                    <textarea class="form-control" id="${fieldId}" name="${field.label}"
                              placeholder="${field.placeholder || ''}" rows="3" ${required}></textarea>
                </div>
            `;
        case 'number':
            return `
                <div class="col-md-6 mb-3">
                    <label for="${fieldId}" class="form-label">${field.label}${requiredMark}</label>
                    <input type="number" class="form-control" id="${fieldId}" name="${field.label}"
                           placeholder="${field.placeholder || ''}" ${required}>
                </div>
            `;
        case 'select':
            let options = '';
            if (field.options && Array.isArray(field.options)) {
                options = field.options.map(opt => `<option value="${opt}">${opt}</option>`).join('');
            }
            return `
                <div class="col-md-6 mb-3">
                    <label for="${fieldId}" class="form-label">${field.label}${requiredMark}</label>
                    <select class="form-select" id="${fieldId}" name="${field.label}" ${required}>
                        <option value="">Choose...</option>
                        ${options}
                    </select>
                </div>
            `;
        case 'checkbox':
            if (field.options && Array.isArray(field.options)) {
                // Multiple checkboxes
                let checkboxes = field.options.map((opt, i) =>
                    `<div class="form-check">
                        <input class="form-check-input" type="checkbox" id="${fieldId}_${i}" name="${field.label}" value="${opt}">
                        <label class="form-check-label" for="${fieldId}_${i}">${opt}</label>
                    </div>`
                ).join('');
                return `
                    <div class="col-md-12 mb-3">
                        <label class="form-label">${field.label}${requiredMark}</label>
                        ${checkboxes}
                    </div>
                `;
            } else {
                // Single checkbox
                return `
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="${fieldId}" name="${field.label}" value="1">
                            <label class="form-check-label" for="${fieldId}">${field.label}${requiredMark}</label>
                        </div>
                    </div>
                `;
            }
        default:
            return `
                <div class="col-md-6 mb-3">
                    <label for="${fieldId}" class="form-label">${field.label}${requiredMark}</label>
                    <input type="text" class="form-control" id="${fieldId}" name="${field.label}"
                           placeholder="${field.placeholder || ''}" ${required}>
                </div>
            `;
    }
}

function submitFormData() {
    if (!currentFormId) {
        showResult('Error', 'Please select a form first', 'error');
        return;
    }

    const formData = collectFormData();
    const userId = document.getElementById('userSelect').value || null;

    const payload = {
        form_id: currentFormId,
        user_id: userId,
        form_data: formData,
        _token: '{{ csrf_token() }}'
    };

    fetch('/manual-forms/add-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify(payload)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showResult('Success', data.message + ` (Submission ID: ${data.submission_id})`, 'success');
            clearForm();
            loadRecentSubmissions(currentFormId);
        } else {
            showResult('Error', data.error || 'Failed to submit form data', 'error');
        }
    })
    .catch(error => {
        console.error('Error submitting form:', error);
        showResult('Error', 'Failed to submit form data', 'error');
    });
}

function collectFormData() {
    const formData = {};
    const container = document.getElementById('formFields');

    // Collect text, email, number, textarea, select inputs
    container.querySelectorAll('input[type="text"], input[type="email"], input[type="number"], textarea, select').forEach(input => {
        if (input.name && input.value) {
            formData[input.name] = input.value;
        }
    });

    // Collect checkboxes
    currentFormStructure.forEach(field => {
        if (field.type === 'checkbox') {
            if (field.options && Array.isArray(field.options)) {
                // Multiple checkboxes
                const checkedValues = [];
                container.querySelectorAll(`input[name="${field.label}"]:checked`).forEach(checkbox => {
                    checkedValues.push(checkbox.value);
                });
                formData[field.label] = checkedValues;
            } else {
                // Single checkbox
                const checkbox = container.querySelector(`input[name="${field.label}"]`);
                formData[field.label] = checkbox && checkbox.checked ? '1' : '0';
            }
        }
    });

    return formData;
}

function clearForm() {
    const container = document.getElementById('formFields');
    container.querySelectorAll('input, textarea, select').forEach(input => {
        if (input.type === 'checkbox') {
            input.checked = false;
        } else {
            input.value = '';
        }
    });
}

function bulkUpload() {
    if (!currentFormId) {
        showResult('Error', 'Please select a form first', 'error');
        return;
    }

    const bulkData = document.getElementById('bulkDataTextarea').value.trim();
    if (!bulkData) {
        showResult('Error', 'Please enter bulk data in JSON format', 'error');
        return;
    }

    let submissions;
    try {
        submissions = JSON.parse(bulkData);
        if (!Array.isArray(submissions)) {
            throw new Error('Data must be an array');
        }
    } catch (e) {
        showResult('Error', 'Invalid JSON format: ' + e.message, 'error');
        return;
    }

    const payload = {
        form_id: currentFormId,
        submissions: submissions,
        _token: '{{ csrf_token() }}'
    };

    fetch('/manual-forms/bulk-add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify(payload)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            let message = data.message;
            if (data.errors && data.errors.length > 0) {
                message += '\n\nErrors:\n' + data.errors.join('\n');
            }
            showResult('Bulk Upload Result', message, data.errors.length > 0 ? 'warning' : 'success');
            document.getElementById('bulkDataTextarea').value = '';
            loadRecentSubmissions(currentFormId);
        } else {
            showResult('Error', data.error || 'Bulk upload failed', 'error');
        }
    })
    .catch(error => {
        console.error('Error in bulk upload:', error);
        showResult('Error', 'Bulk upload failed', 'error');
    });
}

function loadRecentSubmissions(formId) {
    fetch(`/manual-forms/${formId}/submissions`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recentSubmissions');
            if (data.submissions && data.submissions.length > 0) {
                let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>ID</th><th>User ID</th><th>Created At</th><th>Data Preview</th></tr></thead><tbody>';

                data.submissions.slice(0, 10).forEach(submission => {
                    const dataPreview = JSON.stringify(submission).substring(0, 100) + '...';
                    const createdAt = submission.created_at ? new Date(submission.created_at).toLocaleString() : 'N/A';
                    html += `<tr><td>${submission.id}</td><td>${submission.user_id}</td><td>${createdAt}</td><td><small>${dataPreview}</small></td></tr>`;
                });

                html += '</tbody></table></div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<p class="text-muted">No submissions found for this form.</p>';
            }
        })
        .catch(error => {
            console.error('Error loading submissions:', error);
            document.getElementById('recentSubmissions').innerHTML = '<p class="text-danger">Error loading submissions.</p>';
        });
}

function showResult(title, message, type) {
    const modal = new bootstrap.Modal(document.getElementById('resultModal'));
    document.getElementById('resultModalTitle').textContent = title;
    document.getElementById('resultModalBody').innerHTML = `<div class="alert alert-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'success'}">${message.replace(/\n/g, '<br>')}</div>`;
    modal.show();
}
</script>
@endpush
