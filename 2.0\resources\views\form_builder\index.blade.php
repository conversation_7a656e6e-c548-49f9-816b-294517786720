<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Form Builder</title>
</head>
<body>
    <h2>Form Builder</h2>
    <a href="{{ route('admin.form-builder.create') }}">Create New Form</a>

    @if($forms->count() > 0)
        <ul>
        @foreach($forms as $form)
            <li>
                {{ $form->name }} - {{ $form->description }}
                <a href="{{ route('admin.form-builder.edit', $form->id) }}">Edit</a>
                <form method="POST" action="{{ route('admin.form-builder.destroy', $form->id) }}" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit">Delete</button>
                </form>
            </li>
        @endforeach
        </ul>
    @else
        <p>No forms created yet.</p>
    @endif

    <form method="POST" action="/logout">
        @csrf
        <button type="submit">Logout</button>
    </form>
</body>
</html>
