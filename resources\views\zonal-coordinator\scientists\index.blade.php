@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Scientists in My Zone') }}</h5>
                    <div>
                        <a href="{{ route('zonal-coordinator.scientists.assign') }}" class="btn btn-sm btn-success">Assign Scientists</a>
                        <a href="{{ route('zonal-coordinator.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>View all scientists assigned to districts in your zone.</li>
                            <li>Click on a scientist to view their details and district assignments.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Designation</th>
                                    <th>Districts</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="scientistsTableBody">
                                <!-- Scientists will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Scientist Details Modal -->
                    <div class="modal fade" id="scientistModal" tabindex="-1" aria-labelledby="scientistModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="scientistModalLabel">Scientist Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Personal Information</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>Name</th>
                                                    <td id="scientistName"></td>
                                                </tr>
                                                <tr>
                                                    <th>Email</th>
                                                    <td id="scientistEmail"></td>
                                                </tr>
                                                <tr>
                                                    <th>Phone</th>
                                                    <td id="scientistPhone"></td>
                                                </tr>
                                                <tr>
                                                    <th>Designation</th>
                                                    <td id="scientistDesignation"></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>District Assignments</h6>
                                            <div id="districtAssignments">
                                                <!-- District assignments will be loaded here -->
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <h6>Recent Events</h6>
                                            <div id="recentEvents">
                                                <!-- Recent events will be loaded here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const scientistsTableBody = document.getElementById('scientistsTableBody');
        const scientistModal = new bootstrap.Modal(document.getElementById('scientistModal'));

        // Load scientists
        loadScientists();

        // Load scientists function
        function loadScientists() {
            fetch('{{ route("zonal-coordinator.scientists.get-scientists") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        scientistsTableBody.innerHTML = `<tr><td colspan="6" class="text-center text-danger">${data.error}</td></tr>`;
                        return;
                    }

                    scientistsTableBody.innerHTML = '';

                    // Handle case where data has debug_info (no scientists found)
                    if (data.debug_info) {
                        let debugHtml = `
                            <tr>
                                <td colspan="6" class="text-center">
                                    <div class="alert alert-warning">
                                        <h6>No Scientists Found</h6>
                                        <p>${data.debug_info.message}</p>
                                        <details>
                                            <summary>Debug Information (Click to expand)</summary>
                                            <div class="mt-3 text-start">
                                                <p><strong>Your Assigned Districts:</strong></p>
                                                <ul>
                        `;

                        data.debug_info.districts_details.forEach(district => {
                            debugHtml += `<li>ID: ${district.id} - ${district.name} | Scientist: ${district.scientist} | Status: ${district.status}</li>`;
                        });

                        debugHtml += `
                                                </ul>
                                                <p><strong>System Statistics:</strong></p>
                                                <ul>
                                                    <li>Total Scientists in System: ${data.debug_info.total_scientists_in_system}</li>
                                                    <li>Total Districts in System: ${data.debug_info.total_districts_in_system}</li>
                                                    <li>Districts with Scientists: ${data.debug_info.districts_with_scientists}</li>
                                                </ul>
                                            </div>
                                        </details>
                                    </div>
                                </td>
                            </tr>
                        `;
                        scientistsTableBody.innerHTML = debugHtml;
                        return;
                    }

                    // Handle normal case with scientists array
                    const scientists = data.scientists || data;

                    if (scientists.length === 0) {
                        scientistsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No scientists found in your zone. Please contact the administrator to assign scientists to your districts.</td></tr>';
                        return;
                    }

                    scientists.forEach(scientist => {
                        const row = document.createElement('tr');

                        // Create district badges
                        let districtBadges = '';
                        if (scientist.districts && scientist.districts.length > 0) {
                            scientist.districts.forEach(district => {
                                const badgeClass = district.status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info';
                                districtBadges += `<span class="badge ${badgeClass} me-1">${district.district} (${district.status})</span>`;
                            });
                        } else {
                            districtBadges = '<span class="text-muted">No districts assigned</span>';
                        }

                        row.innerHTML = `
                            <td>${scientist.name}</td>
                            <td>${scientist.email}</td>
                            <td>${scientist.phone_number || ''}</td>
                            <td>${scientist.designation || ''}</td>
                            <td>${districtBadges}</td>
                            <td>
                                <button class="btn btn-sm btn-primary view-btn" data-id="${scientist.id}">View Details</button>
                            </td>
                        `;

                        scientistsTableBody.appendChild(row);
                    });

                    // Add event listeners to view buttons
                    document.querySelectorAll('.view-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const scientistId = this.dataset.id;
                            loadScientistDetails(scientistId);
                        });
                    });
                })
                .catch(error => {
                    console.error('Error loading scientists:', error);
                    scientistsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading scientists. Please refresh the page and try again.</td></tr>';
                });
        }

        // Load scientist details function
        function loadScientistDetails(id) {
            fetch(`{{ url("zonal-coordinator/scientists/scientist") }}/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Update scientist information
                    document.getElementById('scientistName').textContent = data.name;
                    document.getElementById('scientistEmail').textContent = data.email;
                    document.getElementById('scientistPhone').textContent = data.phone_number || 'Not provided';
                    document.getElementById('scientistDesignation').textContent = data.designation || 'Not provided';

                    // Update district assignments
                    const districtAssignments = document.getElementById('districtAssignments');

                    if (data.districts && data.districts.length > 0) {
                        const ul = document.createElement('ul');
                        ul.className = 'list-group';

                        data.districts.forEach(district => {
                            const li = document.createElement('li');
                            li.className = 'list-group-item';
                            const badgeClass = district.status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info';
                            li.innerHTML = `
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${district.district}</strong> (${district.state})
                                        <span class="badge ${badgeClass} ms-2">${district.status}</span>
                                    </div>
                                </div>
                            `;
                            ul.appendChild(li);
                        });

                        districtAssignments.innerHTML = '';
                        districtAssignments.appendChild(ul);
                    } else {
                        districtAssignments.innerHTML = '<div class="alert alert-warning">No districts assigned to this scientist.</div>';
                    }

                    // Load recent events
                    loadScientistEvents(id);

                    // Show the modal
                    scientistModal.show();
                })
                .catch(error => {
                    console.error('Error loading scientist details:', error);
                    alert('An error occurred while loading scientist details.');
                });
        }

        // Load scientist events function
        function loadScientistEvents(id) {
            fetch(`{{ url("zonal-coordinator/events/by-scientist") }}/${id}`)
                .then(response => response.json())
                .then(data => {
                    const recentEvents = document.getElementById('recentEvents');

                    if (data.error) {
                        recentEvents.innerHTML = `<div class="alert alert-warning">${data.error}</div>`;
                        return;
                    }

                    if (data.length === 0) {
                        recentEvents.innerHTML = '<div class="alert alert-info">No events found for this scientist.</div>';
                        return;
                    }

                    const table = document.createElement('table');
                    table.className = 'table table-striped table-hover';

                    const thead = document.createElement('thead');
                    thead.innerHTML = `
                        <tr>
                            <th>Title</th>
                            <th>Date</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Participants</th>
                        </tr>
                    `;

                    const tbody = document.createElement('tbody');

                    data.forEach(event => {
                        const row = document.createElement('tr');

                        // Format date
                        const startDate = new Date(event.start_date);
                        const formattedDate = startDate.toLocaleDateString();

                        // Format status
                        let statusBadge = '';
                        if (event.status === 'planned') {
                            statusBadge = '<span class="badge bg-primary">Planned</span>';
                        } else if (event.status === 'completed') {
                            statusBadge = '<span class="badge bg-success">Completed</span>';
                        } else if (event.status === 'cancelled') {
                            statusBadge = '<span class="badge bg-danger">Cancelled</span>';
                        }

                        // Format participants
                        let participants = `${event.actual_participants || 0} / ${event.expected_participants}`;

                        row.innerHTML = `
                            <td>${event.title}</td>
                            <td>${formattedDate}</td>
                            <td>${event.location}</td>
                            <td>${statusBadge}</td>
                            <td>${participants}</td>
                        `;

                        tbody.appendChild(row);
                    });

                    table.appendChild(thead);
                    table.appendChild(tbody);

                    recentEvents.innerHTML = '';
                    recentEvents.appendChild(table);
                })
                .catch(error => {
                    console.error('Error loading scientist events:', error);
                    document.getElementById('recentEvents').innerHTML = '<div class="alert alert-danger">Error loading events.</div>';
                });
        }
    });
</script>
@endpush
