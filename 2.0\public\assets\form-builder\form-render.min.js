/*! For license information please see form-render.min.js.LICENSE.txt */
!function(e){"use strict";!function(){var t={962:function(e,t,r){r.r(t);var n=r(645),o=r.n(n),i=r(278),s=r.n(i)()(o());s.push([e.id,'.rendered-form *{box-sizing:border-box}.rendered-form.formbuilder-embedded-bootstrap button,.rendered-form.formbuilder-embedded-bootstrap input,.rendered-form.formbuilder-embedded-bootstrap select,.rendered-form.formbuilder-embedded-bootstrap textarea{font-family:inherit;font-size:inherit;line-height:inherit}.rendered-form.formbuilder-embedded-bootstrap input{line-height:normal}.rendered-form.formbuilder-embedded-bootstrap textarea{overflow:auto}.rendered-form.formbuilder-embedded-bootstrap button,.rendered-form.formbuilder-embedded-bootstrap input,.rendered-form.formbuilder-embedded-bootstrap select,.rendered-form.formbuilder-embedded-bootstrap textarea{font-family:inherit;font-size:inherit;line-height:inherit}.rendered-form.formbuilder-embedded-bootstrap .btn-group{position:relative;display:inline-block;vertical-align:middle}.rendered-form.formbuilder-embedded-bootstrap .btn-group>.btn{position:relative;float:left}.rendered-form.formbuilder-embedded-bootstrap .btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0}.rendered-form.formbuilder-embedded-bootstrap .btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle){border-radius:0}.rendered-form.formbuilder-embedded-bootstrap .btn-group .btn+.btn,.rendered-form.formbuilder-embedded-bootstrap .btn-group .btn+.btn-group,.rendered-form.formbuilder-embedded-bootstrap .btn-group .btn-group+.btn,.rendered-form.formbuilder-embedded-bootstrap .btn-group .btn-group+.btn-group{margin-left:-1px}.rendered-form.formbuilder-embedded-bootstrap .btn-group>.btn:last-child:not(:first-child),.rendered-form.formbuilder-embedded-bootstrap .btn-group>.dropdown-toggle:not(:first-child),.rendered-form.formbuilder-embedded-bootstrap .btn-group .input-group .form-control:last-child,.rendered-form.formbuilder-embedded-bootstrap .btn-group .input-group-addon:last-child,.rendered-form.formbuilder-embedded-bootstrap .btn-group .input-group-btn:first-child>.btn-group:not(:first-child)>.btn,.rendered-form.formbuilder-embedded-bootstrap .btn-group .input-group-btn:first-child>.btn:not(:first-child),.rendered-form.formbuilder-embedded-bootstrap .btn-group .input-group-btn:last-child>.btn,.rendered-form.formbuilder-embedded-bootstrap .btn-group .input-group-btn:last-child>.btn-group>.btn,.rendered-form.formbuilder-embedded-bootstrap .btn-group .input-group-btn:last-child>.dropdown-toggle{border-top-left-radius:0;border-bottom-left-radius:0}.rendered-form.formbuilder-embedded-bootstrap .btn-group>.btn.active,.rendered-form.formbuilder-embedded-bootstrap .btn-group>.btn:active,.rendered-form.formbuilder-embedded-bootstrap .btn-group>.btn:focus,.rendered-form.formbuilder-embedded-bootstrap .btn-group>.btn:hover{z-index:2}.rendered-form.formbuilder-embedded-bootstrap .btn{display:inline-block;padding:6px 12px;margin-bottom:0;font-size:14px;font-weight:400;line-height:1.42857143;text-align:center;white-space:nowrap;vertical-align:middle;touch-action:manipulation;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none;background-image:none;border-radius:4px}.rendered-form.formbuilder-embedded-bootstrap .btn.btn-lg{padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.rendered-form.formbuilder-embedded-bootstrap .btn.btn-sm{padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.rendered-form.formbuilder-embedded-bootstrap .btn.btn-xs{padding:1px 5px;font-size:12px;line-height:1.5;border-radius:3px}.rendered-form.formbuilder-embedded-bootstrap .btn.active,.rendered-form.formbuilder-embedded-bootstrap .btn.btn-active,.rendered-form.formbuilder-embedded-bootstrap .btn:active{background-image:none}.rendered-form.formbuilder-embedded-bootstrap .input-group .form-control:last-child,.rendered-form.formbuilder-embedded-bootstrap .input-group-addon:last-child,.rendered-form.formbuilder-embedded-bootstrap .input-group-btn:first-child>.btn-group:not(:first-child)>.btn,.rendered-form.formbuilder-embedded-bootstrap .input-group-btn:first-child>.btn:not(:first-child),.rendered-form.formbuilder-embedded-bootstrap .input-group-btn:last-child>.btn,.rendered-form.formbuilder-embedded-bootstrap .input-group-btn:last-child>.btn-group>.btn,.rendered-form.formbuilder-embedded-bootstrap .input-group-btn:last-child>.dropdown-toggle{border-top-left-radius:0;border-bottom-left-radius:0}.rendered-form.formbuilder-embedded-bootstrap .input-group .form-control,.rendered-form.formbuilder-embedded-bootstrap .input-group-addon,.rendered-form.formbuilder-embedded-bootstrap .input-group-btn{display:table-cell}.rendered-form.formbuilder-embedded-bootstrap .input-group-lg>.form-control,.rendered-form.formbuilder-embedded-bootstrap .input-group-lg>.input-group-addon,.rendered-form.formbuilder-embedded-bootstrap .input-group-lg>.input-group-btn>.btn{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333}.rendered-form.formbuilder-embedded-bootstrap .input-group{position:relative;display:table;border-collapse:separate}.rendered-form.formbuilder-embedded-bootstrap .input-group .form-control{position:relative;z-index:2;float:left;width:100%;margin-bottom:0}.rendered-form.formbuilder-embedded-bootstrap .form-control,.rendered-form.formbuilder-embedded-bootstrap output{font-size:14px;line-height:1.42857143;display:block}.rendered-form.formbuilder-embedded-bootstrap textarea.form-control{height:auto}.rendered-form.formbuilder-embedded-bootstrap .form-control{height:34px;display:block;width:100%;padding:6px 12px;font-size:14px;line-height:1.42857143;border-radius:4px}.rendered-form.formbuilder-embedded-bootstrap .form-control:focus{outline:0;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)}.rendered-form.formbuilder-embedded-bootstrap .form-group{margin-left:0px;margin-bottom:15px}.rendered-form.formbuilder-embedded-bootstrap .btn,.rendered-form.formbuilder-embedded-bootstrap .form-control{background-image:none}.rendered-form.formbuilder-embedded-bootstrap .pull-right{float:right}.rendered-form.formbuilder-embedded-bootstrap .pull-left{float:left}.rendered-form .formbuilder-required,.rendered-form .required-asterisk{color:#c10000}.rendered-form .formbuilder-checkbox-group input[type=checkbox],.rendered-form .formbuilder-checkbox-group input[type=radio],.rendered-form .formbuilder-radio-group input[type=checkbox],.rendered-form .formbuilder-radio-group input[type=radio]{margin:0 4px 0 0}.rendered-form .formbuilder-checkbox-inline,.rendered-form .formbuilder-radio-inline{margin-right:8px;display:inline-block;vertical-align:middle;padding-left:0}.rendered-form .formbuilder-checkbox-inline label input[type=text],.rendered-form .formbuilder-radio-inline label input[type=text]{margin-top:0}.rendered-form .formbuilder-checkbox-inline:first-child,.rendered-form .formbuilder-radio-inline:first-child{padding-left:0}.rendered-form .formbuilder-autocomplete-list{background-color:#fff;display:none;list-style:none;padding:0;border:1px solid #ccc;border-width:0 1px 1px;position:absolute;z-index:20;max-height:200px;overflow-y:auto}.rendered-form .formbuilder-autocomplete-list li{display:none;cursor:default;padding:5px;margin:0;transition:background-color 200ms ease-in-out}.rendered-form .formbuilder-autocomplete-list li:hover,.rendered-form .formbuilder-autocomplete-list li.active-option{background-color:rgba(0,0,0,.075)}.rendered-form *[tooltip]{position:relative}.rendered-form *[tooltip]:hover::after{background:rgba(0,0,0,.9);border-radius:5px 5px 5px 0;bottom:23px;color:#fff;content:attr(tooltip);padding:10px 5px;position:absolute;z-index:98;left:2px;width:230px;text-shadow:none;font-size:12px;line-height:1.5em;cursor:default}.rendered-form *[tooltip]:hover::before{border:solid;border-color:#222 rgba(0,0,0,0);border-width:6px 6px 0;bottom:17px;content:"";left:2px;position:absolute;z-index:99;cursor:default}.rendered-form .tooltip-element{visibility:visible;color:#fff;background:#000;width:16px;height:16px;border-radius:8px;display:inline-block;text-align:center;line-height:16px;margin:0 5px;font-size:12px;cursor:default}.rendered-form .kc-toggle{padding-left:0 !important}.rendered-form .kc-toggle span{position:relative;width:48px;height:24px;background:#e6e6e6;display:inline-block;border-radius:4px;border:1px solid #ccc;padding:2px;overflow:hidden;float:left;margin-right:5px;will-change:transform}.rendered-form .kc-toggle span::after,.rendered-form .kc-toggle span::before{position:absolute;display:inline-block;top:0}.rendered-form .kc-toggle span::after{position:relative;content:"";width:50%;height:100%;left:0;border-radius:3px;background:linear-gradient(to bottom, white 0%, #ccc 100%);border:1px solid #999;transition:transform 100ms;transform:translateX(0)}.rendered-form .kc-toggle span::before{border-radius:4px;top:2px;left:2px;content:"";width:calc(100% - 4px);height:18px;box-shadow:0 0 1px 1px #b3b3b3 inset;background-color:rgba(0,0,0,0)}.rendered-form .kc-toggle input{height:0;overflow:hidden;width:0;opacity:0;pointer-events:none;margin:0}.rendered-form .kc-toggle input:checked+span::after{transform:translateX(100%)}.rendered-form .kc-toggle input:checked+span::before{background-color:#6fc665}.rendered-form label{font-weight:normal}.rendered-form .form-group .formbuilder-required{color:#c10000}.rendered-form .other-option:checked+label input{display:inline-block}.rendered-form .other-val{margin-left:5px;display:none}.rendered-form .form-control.number{width:auto}.rendered-form .form-control[type=color]{width:60px;padding:2px;display:inline-block}.rendered-form .form-control[multiple]{height:auto}',""]),t.default=s},278:function(e){e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,n,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var s={};if(n)for(var a=0;a<this.length;a++){var l=this[a][0];null!=l&&(s[l]=!0)}for(var d=0;d<e.length;d++){var c=[].concat(e[d]);n&&s[c[0]]||(void 0!==i&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=i),r&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=r):c[2]=r),o&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=o):c[4]="".concat(o)),t.push(c))}},t}},645:function(e){e.exports=function(e){return e[1]}},252:function(e){e.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=7)}([function(e,t,r){var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=r(2),i=r(10),s=Object.prototype.toString;function a(e){return"[object Array]"===s.call(e)}function l(e){return null!==e&&"object"===(void 0===e?"undefined":n(e))}function d(e){return"[object Function]"===s.call(e)}function c(e,t){if(null!=e)if("object"!==(void 0===e?"undefined":n(e))&&(e=[e]),a(e))for(var r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:a,isArrayBuffer:function(e){return"[object ArrayBuffer]"===s.call(e)},isBuffer:i,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:l,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===s.call(e)},isFile:function(e){return"[object File]"===s.call(e)},isBlob:function(e){return"[object Blob]"===s.call(e)},isFunction:d,isStream:function(e){return l(e)&&d(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:c,merge:function e(){var t={};function r(r,o){"object"===n(t[o])&&"object"===(void 0===r?"undefined":n(r))?t[o]=e(t[o],r):t[o]=r}for(var o=0,i=arguments.length;o<i;o++)c(arguments[o],r);return t},extend:function(e,t,r){return c(t,(function(t,n){e[n]=r&&"function"==typeof t?o(t,r):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},function(e,t,r){(function(t){var n=r(0),o=r(13),i={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!n.isUndefined(e)&&n.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a={adapter:function(){var e;return("undefined"!=typeof XMLHttpRequest||void 0!==t)&&(e=r(3)),e}(),transformRequest:[function(e,t){return o(t,"Content-Type"),n.isFormData(e)||n.isArrayBuffer(e)||n.isBuffer(e)||n.isStream(e)||n.isFile(e)||n.isBlob(e)?e:n.isArrayBufferView(e)?e.buffer:n.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):n.isObject(e)?(s(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};n.forEach(["delete","get","head"],(function(e){a.headers[e]={}})),n.forEach(["post","put","patch"],(function(e){a.headers[e]=n.merge(i)})),e.exports=a}).call(this,r(12))},function(e,t,r){e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}},function(e,t,r){var n=r(0),o=r(14),i=r(16),s=r(17),a=r(18),l=r(4),d="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||r(19);e.exports=function(e){return new Promise((function(t,c){var u=e.data,f=e.headers;n.isFormData(u)&&delete f["Content-Type"];var p=new XMLHttpRequest,m="onreadystatechange",h=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in p||a(e.url)||(p=new window.XDomainRequest,m="onload",h=!0,p.onprogress=function(){},p.ontimeout=function(){}),e.auth){var b=e.auth.username||"",g=e.auth.password||"";f.Authorization="Basic "+d(b+":"+g)}if(p.open(e.method.toUpperCase(),i(e.url,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,p[m]=function(){if(p&&(4===p.readyState||h)&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in p?s(p.getAllResponseHeaders()):null,n={data:e.responseType&&"text"!==e.responseType?p.response:p.responseText,status:1223===p.status?204:p.status,statusText:1223===p.status?"No Content":p.statusText,headers:r,config:e,request:p};o(t,c,n),p=null}},p.onerror=function(){c(l("Network Error",e,null,p)),p=null},p.ontimeout=function(){c(l("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",p)),p=null},n.isStandardBrowserEnv()){var y=r(20),v=(e.withCredentials||a(e.url))&&e.xsrfCookieName?y.read(e.xsrfCookieName):void 0;v&&(f[e.xsrfHeaderName]=v)}if("setRequestHeader"in p&&n.forEach(f,(function(e,t){void 0===u&&"content-type"===t.toLowerCase()?delete f[t]:p.setRequestHeader(t,e)})),e.withCredentials&&(p.withCredentials=!0),e.responseType)try{p.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){p&&(p.abort(),c(e),p=null)})),void 0===u&&(u=null),p.send(u)}))}},function(e,t,r){var n=r(15);e.exports=function(e,t,r,o,i){var s=new Error(e);return n(s,t,r,o,i)}},function(e,t,r){e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,r){function n(e){this.message=e}n.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},n.prototype.__CANCEL__=!0,e.exports=n},function(e,t,r){t.__esModule=!0,t.I18N=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),i=r(8),s={extension:".lang",location:"assets/lang/",langs:["en-US"],locale:"en-US",override:{}},a=t.I18N=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.langs=Object.create(null),this.loaded=[],this.processConfig(t)}return e.prototype.processConfig=function(e){var t=this,r=Object.assign({},s,e),n=r.location,o=function(e,t){var r={};for(var n in e)t.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r}(r,["location"]),i=n.replace(/\/?$/,"/");this.config=Object.assign({},{location:i},o);var a=this.config,l=a.override,d=a.preloaded,c=void 0===d?{}:d,u=Object.entries(this.langs).concat(Object.entries(l||c));this.langs=u.reduce((function(e,r){var n=r[0],o=r[1];return e[n]=t.applyLanguage.call(t,n,o),e}),{}),this.locale=this.config.locale||this.config.langs[0]},e.prototype.init=function(e){return this.processConfig.call(this,Object.assign({},this.config,e)),this.setCurrent(this.locale)},e.prototype.addLanguage=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t="string"==typeof t?this.processFile.call(this,t):t,this.applyLanguage.call(this,e,t),this.config.langs.push("locale")},e.prototype.getValue=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.locale;return this.langs[t]&&this.langs[t][e]||this.getFallbackValue(e)},e.prototype.getFallbackValue=function(e){var t=Object.values(this.langs).find((function(t){return t[e]}));return t&&t[e]},e.prototype.makeSafe=function(e){var t={"{":"\\{","}":"\\}","|":"\\|"};return e=e.replace(/\{|\}|\|/g,(function(e){return t[e]})),new RegExp(e,"g")},e.prototype.put=function(e,t){return this.current[e]=t},e.prototype.get=function(e,t){var r=this.getValue(e);if(r){var o=r.match(/\{[^}]+?\}/g),i=void 0;if(t&&o)if("object"===(void 0===t?"undefined":n(t)))for(var s=0;s<o.length;s++)i=o[s].substring(1,o[s].length-1),r=r.replace(this.makeSafe(o[s]),t[i]||"");else r=r.replace(/\{[^}]+?\}/g,t);return r}},e.prototype.fromFile=function(e){for(var t,r=e.split("\n"),n={},o=0;o<r.length;o++)(t=r[o].match(/^(.+?) *?= *?([^\n]+)/))&&(n[t[1]]=t[2].replace(/^\s+|\s+$/,""));return n},e.prototype.processFile=function(e){return this.fromFile(e.replace(/\n\n/g,"\n"))},e.prototype.loadLang=function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=this;return new Promise((function(n,o){if(-1!==r.loaded.indexOf(e)&&t)return r.applyLanguage.call(r,r.langs[e]),n(r.langs[e]);var s=[r.config.location,e,r.config.extension].join("");return(0,i.get)(s).then((function(t){var o=t.data,i=r.processFile(o);return r.applyLanguage.call(r,e,i),r.loaded.push(e),n(r.langs[e])})).catch((function(){var t=r.applyLanguage.call(r,e);n(t)}))}))},e.prototype.applyLanguage=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.config.override[e]||{},n=this.langs[e]||{};return this.langs[e]=Object.assign({},n,t,r),this.langs[e]},e.prototype.setCurrent=function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en-US";return this.loadLang(t).then((function(){return e.locale=t,e.current=e.langs[t],e.current}))},o(e,[{key:"getLangs",get:function(){return this.config.langs}}]),e}();t.default=new a},function(e,t,r){e.exports=r(9)},function(e,t,r){var n=r(0),o=r(2),i=r(11),s=r(1);function a(e){var t=new i(e),r=o(i.prototype.request,t);return n.extend(r,i.prototype,t),n.extend(r,t),r}var l=a(s);l.Axios=i,l.create=function(e){return a(n.merge(s,e))},l.Cancel=r(6),l.CancelToken=r(26),l.isCancel=r(5),l.all=function(e){return Promise.all(e)},l.spread=r(27),e.exports=l,e.exports.default=l},function(e,t,r){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},function(e,t,r){var n=r(1),o=r(0),i=r(21),s=r(22);function a(e){this.defaults=e,this.interceptors={request:new i,response:new i}}a.prototype.request=function(e){"string"==typeof e&&(e=o.merge({url:arguments[0]},arguments[1])),(e=o.merge(n,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var t=[s,void 0],r=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)r=r.then(t.shift(),t.shift());return r},o.forEach(["delete","get","head","options"],(function(e){a.prototype[e]=function(t,r){return this.request(o.merge(r||{},{method:e,url:t}))}})),o.forEach(["post","put","patch"],(function(e){a.prototype[e]=function(t,r,n){return this.request(o.merge(n||{},{method:e,url:t,data:r}))}})),e.exports=a},function(e,t,r){var n,o,i=e.exports={};function s(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function l(e){if(n===setTimeout)return setTimeout(e,0);if((n===s||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:s}catch(e){n=s}try{o="function"==typeof clearTimeout?clearTimeout:a}catch(e){o=a}}();var d,c=[],u=!1,f=-1;function p(){u&&d&&(u=!1,d.length?c=d.concat(c):f=-1,c.length&&m())}function m(){if(!u){var e=l(p);u=!0;for(var t=c.length;t;){for(d=c,c=[];++f<t;)d&&d[f].run();f=-1,t=c.length}d=null,u=!1,function(e){if(o===clearTimeout)return clearTimeout(e);if((o===a||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(e);try{o(e)}catch(t){try{return o.call(null,e)}catch(t){return o.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function b(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new h(e,t)),1!==c.length||u||l(m)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=b,i.addListener=b,i.once=b,i.off=b,i.removeListener=b,i.removeAllListeners=b,i.emit=b,i.prependListener=b,i.prependOnceListener=b,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,r){var n=r(0);e.exports=function(e,t){n.forEach(e,(function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])}))}},function(e,t,r){var n=r(4);e.exports=function(e,t,r){var o=r.config.validateStatus;r.status&&o&&!o(r.status)?t(n("Request failed with status code "+r.status,r.config,null,r.request,r)):e(r)}},function(e,t,r){e.exports=function(e,t,r,n,o){return e.config=t,r&&(e.code=r),e.request=n,e.response=o,e}},function(e,t,r){var n=r(0);function o(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,r){if(!t)return e;var i;if(r)i=r(t);else if(n.isURLSearchParams(t))i=t.toString();else{var s=[];n.forEach(t,(function(e,t){null!=e&&(n.isArray(e)?t+="[]":e=[e],n.forEach(e,(function(e){n.isDate(e)?e=e.toISOString():n.isObject(e)&&(e=JSON.stringify(e)),s.push(o(t)+"="+o(e))})))})),i=s.join("&")}return i&&(e+=(-1===e.indexOf("?")?"?":"&")+i),e}},function(e,t,r){var n=r(0),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,r,i,s={};return e?(n.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=n.trim(e.substr(0,i)).toLowerCase(),r=n.trim(e.substr(i+1)),t){if(s[t]&&o.indexOf(t)>=0)return;s[t]="set-cookie"===t?(s[t]?s[t]:[]).concat([r]):s[t]?s[t]+", "+r:r}})),s):s}},function(e,t,r){var n=r(0);e.exports=n.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function o(e){var n=e;return t&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return e=o(window.location.href),function(t){var r=n.isString(t)?o(t):t;return r.protocol===e.protocol&&r.host===e.host}}():function(){return!0}},function(e,t,r){function n(){this.message="String contains an invalid character"}n.prototype=new Error,n.prototype.code=5,n.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,r,o=String(e),i="",s=0,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";o.charAt(0|s)||(a="=",s%1);i+=a.charAt(63&t>>8-s%1*8)){if((r=o.charCodeAt(s+=.75))>255)throw new n;t=t<<8|r}return i}},function(e,t,r){var n=r(0);e.exports=n.isStandardBrowserEnv()?{write:function(e,t,r,o,i,s){var a=[];a.push(e+"="+encodeURIComponent(t)),n.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),n.isString(o)&&a.push("path="+o),n.isString(i)&&a.push("domain="+i),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t,r){var n=r(0);function o(){this.handlers=[]}o.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){n.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},function(e,t,r){var n=r(0),o=r(23),i=r(5),s=r(1),a=r(24),l=r(25);function d(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return d(e),e.baseURL&&!a(e.url)&&(e.url=l(e.baseURL,e.url)),e.headers=e.headers||{},e.data=o(e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),n.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||s.adapter)(e).then((function(t){return d(e),t.data=o(t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(d(e),t&&t.response&&(t.response.data=o(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},function(e,t,r){var n=r(0);e.exports=function(e,t,r){return n.forEach(r,(function(r){e=r(e,t)})),e}},function(e,t,r){e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},function(e,t,r){e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,r){var n=r(6);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var r=this;e((function(e){r.reason||(r.reason=new n(e),t(r.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},function(e,t,r){e.exports=function(e){return function(t){return e.apply(null,t)}}}])},700:function(e,t,r){var n=r(292),o=r(962);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.id,o,""]]);n(o,{attributes:{class:"formBuilder-injected-style"},insert:"head",singleton:!1}),e.exports=o.locals||{}},292:function(e,t,r){var n,o=function(){var e={};return function(t){if(void 0===e[t]){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}e[t]=r}return e[t]}}(),i=[];function s(e){for(var t=-1,r=0;r<i.length;r++)if(i[r].identifier===e){t=r;break}return t}function a(e,t){for(var r={},n=[],o=0;o<e.length;o++){var a=e[o],l=t.base?a[0]+t.base:a[0],d=r[l]||0,c="".concat(l," ").concat(d);r[l]=d+1;var u=s(c),f={css:a[1],media:a[2],sourceMap:a[3]};-1!==u?(i[u].references++,i[u].updater(f)):i.push({identifier:c,updater:h(f,t),references:1}),n.push(c)}return n}function l(e){var t=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var i=r.nc;i&&(n.nonce=i)}if(Object.keys(n).forEach((function(e){t.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(t);else{var s=o(e.insert||"head");if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(t)}return t}var d,c=(d=[],function(e,t){return d[e]=t,d.filter(Boolean).join("\n")});function u(e,t,r,n){var o=r?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=c(t,o);else{var i=document.createTextNode(o),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(i,s[t]):e.appendChild(i)}}function f(e,t,r){var n=r.css,o=r.media,i=r.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var p=null,m=0;function h(e,t){var r,n,o;if(t.singleton){var i=m++;r=p||(p=l(t)),n=u.bind(null,r,i,!1),o=u.bind(null,r,i,!0)}else r=l(t),n=f.bind(null,r,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(r)};return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=(void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n));var r=a(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<r.length;n++){var o=s(r[n]);i[o].references--}for(var l=a(e,t),d=0;d<r.length;d++){var c=s(r[d]);0===i[c].references&&(i[c].updater(),i.splice(c,1))}r=l}}}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var i=r[e]={id:e,exports:{}};return t[e](i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nc=void 0,function(){var t=n(252),r=n.n(t);const o={clobberingProtection:{document:!0,form:!0,namespaceAttributes:!1},backendOrder:["dompurify","sanitizer","fallback"],backends:{sanitizer:"function"==typeof window.Sanitizer&&new window.Sanitizer,dompurify:!!window.DOMPurify&&(i=window.DOMPurify,i.setConfig({SANITIZE_DOM:!1,ADD_ATTR:["contenteditable"]}),i),fallback:e=>e}};var i;const s=(e,t)=>{if(0===o.backendOrder.length)return!1;const r=e.toLowerCase();return t=t?t+"":"",r.startsWith("on")||["form","formaction"].includes(r)||t.trim().toLowerCase().startsWith("javascript:")};o.backends.fallback=function(t){const r=document.implementation.createHTMLDocument(""),n=r.createElement("base");n.href=document.location.href,r.head.appendChild(n);const o=["applet","comment","embed","iframe","link","listing","meta","noscript","object","plaintext","script","style","xmp"],i=e.parseHTML(t,r,!1);e(i).find("*").addBack().each(((t,r)=>{"#text"!==r.nodeName&&(r.tagName&&o.includes(r.tagName.toLowerCase())?r.parentElement?r.parentElement.removeChild(r):i.includes(r)&&i.splice(i.indexOf(r),1):r.attributes&&Array.from(r.attributes).forEach((t=>{s(t.name,t.value)&&e(r).removeAttr(t.name)})))}));const a=r.createElement("div");return e(a).html(i),a.innerHTML};const a=e=>{const t=!!o.clobberingProtection.document&&document,r=!!o.clobberingProtection.form&&document.createElement("form");return t&&e in t||r&&e in r?o.clobberingProtection.namespaceAttributes?"user-content-"+e:void 0:e},l={fallback:(e,t)=>{const r=o.backends.fallback,n="function"==typeof r;return n&&(t=r(t)),e.innerHTML=t,n},dompurify:(e,t)=>{const r=o.backends.dompurify;return!(!1===r||!r.isSupported||(e.innerHTML=r.sanitize(t),0))},sanitizer:(e,t)=>{const r=o.backends.sanitizer;return!!r&&(e.setHTML(t,{sanitizer:r}),!0)}},d=["events"],c=["tag","content"];function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}window.fbLoaded={js:[],css:[]},window.fbEditors={quill:{},tinymce:{}};const h=function(e,t=!1){if(null==e||"object"!=typeof e)return e;const r="function"==typeof window.structuredClone?window.structuredClone(e):Object.assign({},e),n=[null,void 0,""];t&&n.push(!1);for(const e in r)n.includes(r[e])?delete r[e]:Array.isArray(r[e])&&(r[e].length||delete r[e]);return r},b=function(e){return!["values","enableOther","other","label","subtype"].includes(e)},g=(e,t)=>{let r;return e=y(e),t&&(Array.isArray(t)?r=T(t.join(" ")):("boolean"==typeof t&&(t=t.toString()),r=T(t.trim()))),{name:e,value:t=t?`="${r}"`:""}},y=e=>({className:"class"}[e]||v(e)),v=e=>(e=(e=e.replace(/[^\w\s\-]/gi,"")).replace(/([A-Z])/g,(function(e){return"-"+e.toLowerCase()}))).replace(/\s/g,"-").replace(/^-+/g,""),w=e=>e.replace(/-([a-z])/g,((e,t)=>t.toUpperCase())),x=function(){let e,t=0;return function(r){const n=Date.now();return n===e?++t:(t=0,e=n),(r.type||v(r.label))+"-"+n+"-"+t}}(),O=e=>void 0===e?e:[["array",e=>Array.isArray(e)],["node",e=>e instanceof window.Node||e instanceof window.HTMLElement],["component",()=>e&&e.dom],[typeof e,()=>!0]].find((t=>t[1](e)))[0],j=function(t,r="",n={}){let i=O(r);const{events:u}=n,f=m(n,d),p=document.createElement(t),h={string:t=>{((t,r,n=!1)=>{if(!n){const n=document.createElement(t.tagName);return void 0!==o.backendOrder.find((e=>l[e](n,r)))?((t=>{e(t).find("*").each(((e,t)=>{["embed","form","iframe","image","img","object"].includes(t.tagName.toLowerCase())&&t.removeAttribute("name"),["id","name"].forEach((e=>{if(t.hasAttribute(e)){const r=a(t.getAttribute(e));void 0===r?t.removeAttribute(e):t.setAttribute(e,r)}}))}))})(n),t.innerHTML=n.innerHTML,t):(t.innerHTML=r,t)}t.textContent=r})(p,p.innerHTML+t)},object:e=>{const{tag:t,content:r}=e,n=m(e,c);return p.appendChild(j(t,r,n))},node:e=>p.appendChild(e),array:e=>{for(let t=0;t<e.length;t++)i=O(e[t]),h[i](e[t])},function:e=>{e=e(),i=O(e),h[i](e)},undefined:()=>{}};for(const e in f)if(f.hasOwnProperty(e)){const t=y(e);let r=Array.isArray(f[e])?P(f[e].join(" ").split(" ")).join(" "):f[e];if(s(t,r))continue;if("boolean"==typeof r){if(!0===r){const e="contenteditable"===t||t;p.setAttribute(t,e)}}else"id"!==t&&"name"!==t||(r=a(r)),void 0!==r&&p.setAttribute(t,r)}return r&&h[i](r),((e,t)=>{if(t)for(const r in t)t.hasOwnProperty(r)&&e.addEventListener(r,(e=>t[r](e)))})(p,u),p},k=e=>{const t=e.attributes,r={};return L(t,(e=>{let n=t[e].value||"";n.match(/false|true/g)?n="true"===n:n.match(/undefined/g)&&(n=void 0),n&&(r[w(t[e].name)]=n)})),r},C=e=>{const t=[];for(let r=0;r<e.length;r++){const n=f(f({},k(e[r])),{},{label:e[r].textContent});t.push(n)}return t},S=e=>{const t=[];if(e.length){const r=e[0].getElementsByTagName("value");for(let e=0;e<r.length;e++)t.push(r[e].textContent)}return t},E=e=>{const t=(new window.DOMParser).parseFromString(e,"text/xml"),r=[];if(t){const e=t.getElementsByTagName("field");for(let t=0;t<e.length;t++){const n=k(e[t]),o=e[t].getElementsByTagName("option"),i=e[t].getElementsByTagName("userData");o&&o.length&&(n.values=C(o)),i&&i.length&&(n.userData=S(i)),r.push(n)}}return r},A=e=>{const t=document.createElement("textarea");return t.innerHTML=e,t.textContent},T=e=>{const t={'"':"&quot;","&":"&amp;","<":"&lt;",">":"&gt;"};return"string"==typeof e?e.replace(/["&<>]/g,(e=>t[e]||e)):e},L=function(e,t,r){for(let n=0;n<e.length;n++)t.call(r,n,e[n])},P=e=>e.filter(((e,t,r)=>r.indexOf(e)===t)),R=(e,t="")=>{const r=jQuery;let n=[];return Array.isArray(e)||(e=[e]),D(e)||(n=jQuery.map(e,(e=>{const r={dataType:"script",cache:!0,url:(t||"")+e};return jQuery.ajax(r).done((()=>window.fbLoaded.js.push(e)))}))),n.push(jQuery.Deferred((e=>r(e.resolve)))),jQuery.when(...n)},D=(e,t="js")=>{const r=window.fbLoaded[t];return Array.isArray(e)?e.every((e=>r.includes(e))):r.includes(e)},N=(t,r="")=>{Array.isArray(t)||(t=[t]),t.forEach((t=>{let n="href",o=t,i="";if("object"==typeof t&&(n=t.type||(t.style?"inline":"href"),i=t.id,o=i||t.href||t.style,t="inline"===n?t.style:t.href),!D(o,"css")){if("href"===n){const e=document.createElement("link");e.type="text/css",e.rel="stylesheet",e.href=(r||"")+t,document.head.appendChild(e)}else e(`<style type="text/css">${t}</style>`).attr("id",i).appendTo(e(document.head));window.fbLoaded.css.push(o)}}))},q=(e,t)=>{const r=Object.assign({},e,t);for(const n in t)r.hasOwnProperty(n)&&(Array.isArray(t[n])?r[n]=Array.isArray(e[n])?P(e[n].concat(t[n])):t[n]:"object"==typeof t[n]?r[n]=q(e[n],t[n]):r[n]=t[n]);return r},M=/^col-(xs|sm|md|lg)-([^\s]+)/,F={addEventListeners:(e,t,r)=>t.split(" ").forEach((t=>e.addEventListener(t,r,!1))),attrString:e=>Object.entries(e).map((([e,t])=>b(e)&&Object.values(g(e,t)).join(""))).filter(Boolean).join(" "),camelCase:w,capitalize:e=>e.replace(/\b\w/g,(function(e){return e.toUpperCase()})),closest:(e,t)=>{const r=t.replace(".","");for(;(e=e.parentElement)&&!e.classList.contains(r););return e},getContentType:O,escapeAttr:T,escapeAttrs:e=>{for(const t in e)e.hasOwnProperty(t)&&(e[t]=T(e[t]));return e},escapeHtml:e=>{const t=document.createElement("textarea");return t.textContent=e,t.innerHTML},forceNumber:e=>e.replace(/[^0-9]/g,""),forEach:L,getScripts:R,getStyles:N,hyphenCase:v,isCached:D,markup:j,merge:q,mobileClass:()=>{let e="";var t;return t=navigator.userAgent||navigator.vendor||window.opera,/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(t)&&(e="formbuilder-mobile"),e},nameAttr:x,parsedHtml:A,parseXML:E,removeFromArray:(e,t)=>{const r=t.indexOf(e);r>-1&&t.splice(r,1)},safeAttr:g,safeAttrName:y,safename:e=>e.replace(/\s/g,"-").replace(/[^a-zA-Z0-9[\]_-]/g,""),subtract:(e,t)=>t.filter((function(e){return!~this.indexOf(e)}),e),trimObj:h,unique:P,validAttr:b,titleCase:function(e){const t=["a","an","and","as","at","but","by","for","for","from","in","into","near","nor","of","on","onto","or","the","to","with"].map((e=>`\\s${e}\\s`)),r=new RegExp(`(?!${t.join("|")})\\w\\S*`,"g");return`${e}`.replace(r,(e=>e.charAt(0).toUpperCase()+e.slice(1).replace(/[A-Z]/g,(e=>` ${e}`))))},firstNumberOrUndefined:function(...e){return e.find((e=>"number"==typeof e))},splitObject:(e,t)=>{const r=e=>(t,r)=>(t[r]=e[r],t);return[Object.keys(e).filter((e=>t.includes(e))).reduce(r(e),{}),Object.keys(e).filter((e=>!t.includes(e))).reduce(r(e),{})]}};e.fn.swapWith=function(t){const r=this,n=e(t),o=e("<div>");return r.before(o),n.before(r),o.before(n).remove(),r};var z=F;const B=(e,t,r=!0)=>{const n=[];let o=["none","block"];r&&(o=o.reverse());for(let r=e.length-1;r>=0;r--)-1!==e[r].textContent.toLowerCase().indexOf(t.toLowerCase())?(e[r].style.display=o[0],n.push(e[r])):e[r].style.display=o[1];return n};function H(e){let t;return"function"==typeof Event?t=new Event(e):(t=document.createEvent("Event"),t.initEvent(e,!0,!0)),t}new RegExp(`(${["select","checkbox-group","checkbox","radio-group","autocomplete"].join("|")})`);var $={loaded:H("loaded"),viewData:H("viewData"),userDeclined:H("userDeclined"),modalClosed:H("modalClosed"),modalOpened:H("modalOpened"),formSaved:H("formSaved"),fieldAdded:H("fieldAdded"),fieldRemoved:H("fieldRemoved"),fieldRendered:H("fieldRendered"),fieldEditOpened:H("fieldEditOpened"),fieldEditClosed:H("fieldEditClosed"),stageEmptied:H("stageEmptied")};const U=["label","type"];class I{constructor(e,t){this.rawConfig=jQuery.extend({},e),e=jQuery.extend({},e),this.preview=t,delete e.isPreview,this.preview&&delete e.required;const r=["label","description","subtype","required","disabled"];for(const t of r)this[t]=e[t],delete e[t];e.id||(e.name?e.id=e.name:e.id="control-"+Math.floor(1e7*Math.random()+1)),this.id=e.id,this.type=e.type,this.description&&(e.title=this.description),I.controlConfig||(I.controlConfig={});const n=this.subtype?this.type+"."+this.subtype:this.type;this.classConfig=jQuery.extend({},I.controlConfig[this.type]||{},I.controlConfig[n]||{}),this.subtype&&(e.type=this.subtype),this.required&&(e.required="required",e["aria-required"]="true"),this.disabled&&(e.disabled="disabled"),this.config=e,this.configure()}static get definition(){return{}}static register(e,t,r){const n=r?r+".":"";I.classRegister||(I.classRegister={}),Array.isArray(e)||(e=[e]);for(const r of e)-1===r.indexOf(".")?I.classRegister[n+r]=t:I.error(`Ignoring type ${r}. Cannot use the character '.' in a type name.`)}static getRegistered(e=!1){const t=Object.keys(I.classRegister);return t.length?t.filter((t=>e?t.indexOf(e+".")>-1:-1===t.indexOf("."))):t}static getRegisteredSubtypes(){const e={};for(const t in I.classRegister)if(I.classRegister.hasOwnProperty(t)){const[r,n]=t.split(".");if(!n)continue;e[r]||(e[r]=[]),e[r].push(n)}return e}static getClass(e,t){const r=t?e+"."+t:e;return I.classRegister[r]||I.classRegister[e]||I.error("Invalid control type. (Type: "+e+", Subtype: "+t+"). Please ensure you have registered it, and imported it correctly.")}static loadCustom(e){let t=[];if(e&&(t=t.concat(e)),window.fbControls&&(t=t.concat(window.fbControls)),!this.fbControlsLoaded){for(const e of t)e(I,I.classRegister);this.fbControlsLoaded=!0}}static mi18n(e,t){const n=this.definition;let o=n.i18n||{};o=o[r().locale]||o.default||o;const i=this.camelCase(e),s="object"==typeof o?o[i]||o[e]:o;if(s)return s;let a=n.mi18n;return"object"==typeof a&&(a=a[i]||a[e]),a||(a=i),r().get(a,t)}static active(e){return!Array.isArray(this.definition.inactive)||-1===this.definition.inactive.indexOf(e)}static label(e){return this.mi18n(e)}static icon(e){const t=this.definition;return t&&"object"==typeof t.icon?t.icon[e]:t.icon}configure(){}build(){const e=this.config,{label:t,type:r}=e,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,U);return this.markup(r,A(t),n)}on(e){const t={prerender:e=>e,render:e=>{const t=()=>{this.onRender&&this.onRender(e)};this.css&&N(this.css),this.js&&!D(this.js)?R(this.js).done(t):t()}};return e?t[e]:t}static error(e){throw new Error(e)}markup(e,t="",r={}){return this.element=j(e,t,r),this.element}parsedHtml(e){return A(e)}static camelCase(e){return w(e)}}const _=(e,t)=>{let r=e.id?`formbuilder-${e.type} form-group field-${e.id}`:"";if(e.className){const n=(e=>"string"==typeof e?e.split(" ").filter((e=>M.test(e)||e.startsWith("row-"))):[])(e.className);n&&n.length>0&&(r+=` ${n.join(" ")}`,Array.isArray(t)||(t=[t]),t.forEach((e=>{e.classList&&e.classList.remove(...n),e.querySelectorAll("[class*=row-],[class*=col-]").forEach((e=>{e.classList&&e.classList.remove(...n)}))})))}return r};class V{constructor(e,t=!1,r=!1,n={}){this.preview=null!=t&&t,this.disableHTMLLabels=null!=r&&r,this.controlConfig=null!=n?n:{},this.templates={label:null,help:null,default:(e,t,r,n)=>(r&&t.appendChild(r),this.markup("div",[t,e],{className:_(n,e)})),noLabel:(e,t,r,n)=>this.markup("div",e,{className:_(n,e)}),hidden:e=>e},e&&(this.templates=jQuery.extend(this.templates,e)),this.configure()}configure(){}build(e,t,r){this.preview&&(t.name?t.name=t.name+"-preview":t.name=z.nameAttr(t)+"-preview"),t.id=t.name,this.data=jQuery.extend({},t),I.controlConfig=this.controlConfig;const n=new e(t,this.preview);let o=n.build();if("object"==typeof o&&o.field||(o={field:o}),"string"==typeof o.field){const e=this.markup("div",o.field,{});1===e.childElementCount?o.field=e.children.item(0):o.field=Array.from(e.children)}const i=this.label(),s=this.help();let a;a=r&&this.isTemplate(r)?r:this.isTemplate(o.layout)?o.layout:"default";const l=this.processTemplate(a,o.field,i,s);return n.on("prerender")(l),l.addEventListener("fieldRendered",n.on("render")),l}label(){const e=this.data.label||"",t=[this.disableHTMLLabels?document.createTextNode(e):z.parsedHtml(e)];return this.data.required&&t.push(this.markup("span","*",{className:"formbuilder-required"})),this.isTemplate("label")?this.processTemplate("label",t):this.markup("label",t,{for:this.data.id,className:`formbuilder-${this.data.type}-label`})}help(){return this.data.description?this.isTemplate("help")?this.processTemplate("help",this.data.description):this.markup("span","?",{className:"tooltip-element",tooltip:this.data.description}):null}isTemplate(e){return"function"==typeof this.templates[e]}processTemplate(e,...t){let r=this.templates[e](...t,this.data);return r.jquery&&(r=r[0]),r}markup(e,t="",r={}){return z.markup(e,t,r)}}const Q=["values","type"];I.register("autocomplete",class extends I{static get definition(){return{mi18n:{requireValidOption:"requireValidOption"}}}build(){const e=this.config,{values:t,type:r}=e,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,Q),o=e=>{const t=e.target.nextSibling.nextSibling,r=e.target.nextSibling,n=this.getActiveOption(t);let o=new Map([[38,()=>{const e=this.getPreviousOption(n);e&&this.selectOption(t,e)}],[40,()=>{const e=this.getNextOption(n);e&&this.selectOption(t,e)}],[13,()=>{n?(e.target.value=n.innerHTML,r.value=n.getAttribute("value"),"none"===t.style.display?this.showList(t,n):this.hideList(t)):this.config.requireValidOption&&(this.isOptionValid(t,e.target.value)||(e.target.value="",e.target.nextSibling.value="")),e.preventDefault()}],[27,()=>{this.hideList(t)}]]).get(e.keyCode);return o||(o=()=>!1),o()},i={focus:e=>{const t=e.target.nextSibling.nextSibling,r=B(t.querySelectorAll("li"),e.target.value);if(e.target.addEventListener("keydown",o),e.target.value.length>0){const e=r.length>0?r[r.length-1]:null;this.showList(t,e)}},blur:e=>{e.target.removeEventListener("keydown",o);const t=setTimeout((()=>{e.target.nextSibling.nextSibling.style.display="none",clearTimeout(t)}),200);if(this.config.requireValidOption){const t=e.target.nextSibling.nextSibling;this.isOptionValid(t,e.target.value)||(e.target.value="",e.target.nextSibling.value="")}},input:e=>{const t=e.target.nextSibling.nextSibling;e.target.nextSibling.value=e.target.value;const r=B(t.querySelectorAll("li"),e.target.value);if(0==r.length)this.hideList(t);else{let e=this.getActiveOption(t);e||(e=r[r.length-1]),this.showList(t,e)}}},s=Object.assign({},n,{id:`${n.id}-input`,autocomplete:"off",events:i}),a=Object.assign({},n,{type:"hidden"});delete s.name;const l=[this.markup("input",null,s),this.markup("input",null,a)],d=t.map((e=>{const t=e.label,r={events:{click:t=>{const r=t.target.parentElement,n=r.previousSibling.previousSibling;n.value=e.label,n.nextSibling.value=e.value,this.hideList(r)}},value:e.value};return this.markup("li",t,r)}));return l.push(this.markup("ul",d,{id:`${n.id}-list`,className:`formbuilder-${r}-list`})),l}hideList(e){this.selectOption(e,null),e.style.display="none"}showList(e,t){this.selectOption(e,t),e.style.display="block",e.style.width=e.parentElement.offsetWidth+"px"}getActiveOption(e){const t=e.getElementsByClassName("active-option")[0];return t&&"none"!==t.style.display?t:null}getPreviousOption(e){let t=e;do{t=t?t.previousSibling:null}while(null!=t&&"none"===t.style.display);return t}getNextOption(e){let t=e;do{t=t?t.nextSibling:null}while(null!=t&&"none"===t.style.display);return t}selectOption(e,t){const r=e.querySelectorAll("li");for(let e=0;e<r.length;e++)r[e].classList.remove("active-option");t&&t.classList.add("active-option")}isOptionValid(e,t){const r=e.querySelectorAll("li");let n=!1;for(let e=0;e<r.length;e++)if(r[e].innerHTML===t){n=!0;break}return n}onRender(t){if(this.config.userData){const t=e("#"+this.config.name),r=t.next(),n=this.config.userData[0];let o=null;if(r.find("li").each((function(){e(this).attr("value")===n&&(o=e(this).get(0))})),null===o)return this.config.requireValidOption?void 0:void t.prev().val(this.config.userData[0]);t.prev().val(o.innerHTML),t.val(o.getAttribute("value"));const i=t.next().get(0);"none"===i.style.display?this.showList(i,o):this.hideList(i)}return t}});class X extends I{build(){return{field:this.markup("button",this.label,this.config),layout:"noLabel"}}}I.register("button",X),I.register(["button","submit","reset"],X,"button");class W extends I{constructor(e,t,r){super(e,t),this.template=r}build(){let e=this.template;if(!e)return I.error(`Invalid custom control type '${this.type}'. Please ensure you have registered it correctly as a template option.`);const t=Object.assign(this.config),r=["label","description","subtype","id","preview","required","title","aria-required","type"];for(const e of r)t[e]=this.config[e]||this[e];return e=e.bind(this),e=e(t),e.js&&(this.js=e.js),e.css&&(this.css=e.css),this.onRender=e.onRender,{field:e.field,layout:e.layout}}}I.register("hidden",class extends I{build(){return this.field=this.markup("input",null,this.config),{field:this.field,layout:"hidden"}}onRender(){this.config.userData&&e(this.field).val(this.config.userData[0])}});const J=["type"];class G extends I{build(){const e=this.config,{type:t}=e,r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,J);let n=t;const o={paragraph:"p",header:this.subtype};return o[t]&&(n=o[t]),{field:this.markup(n,z.parsedHtml(this.label),r),layout:"noLabel"}}}I.register(["paragraph","header"],G),I.register(["p","address","blockquote","canvas","output"],G,"paragraph"),I.register(["h1","h2","h3","h4","h5","h6"],G,"header");const Z=["values","value","placeholder","type","inline","other","toggle"],K=["label"];function Y(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}I.register(["select","checkbox-group","radio-group","checkbox"],class extends I{static get definition(){return{inactive:["checkbox"],mi18n:{minSelectionRequired:"minSelectionRequired"}}}build(){const e=[],t=this.config,{values:r,value:n,placeholder:o,type:i,inline:s,other:a,toggle:l}=t,d=Y(t,Z),c=i.replace("-group",""),u="select"===i;if((d.multiple||"checkbox-group"===i)&&(d.name=d.name+"[]"),("checkbox-group"===i||"radio-group"===i)&&d.required){const e=this,t=this.onRender.bind(this);this.onRender=function(){t(),e.groupRequired()}}if(delete d.title,r){o&&u&&e.push(this.markup("option",o,{disabled:!0,selected:!0,value:""}));for(let t=0;t<r.length;t++){let i=r[t];"string"==typeof i&&(i={label:i,value:i});const{label:a=""}=i,f=Y(i,K);if(f.id=`${d.id}-${t}`,f.selected&&!o||delete f.selected,void 0!==n&&f.value===n&&(f.selected=!0),u){const t=this.markup("option",document.createTextNode(a),f);e.push(t)}else{const t=[a];let r=`formbuilder-${c}`;s&&(r+="-inline"),f.type=c,f.selected&&(f.checked="checked",delete f.selected);const n=this.markup("input",null,Object.assign({},d,f)),o={for:f.id};let i=[n,this.markup("label",t,o)];l&&(delete o.for,o.className="kc-toggle",t.unshift(n,this.markup("span")),i=this.markup("label",t,o));const u=this.markup("div",i,{className:r});e.push(u)}}if(!u&&a){var f;const t={id:`${d.id}-other`,className:`${null!==(f=d.className)&&void 0!==f?f:""} other-option`,value:""};let r=`formbuilder-${c}`;s&&(r+="-inline");const n=Object.assign({},d,t);n.type=c;const o={type:"text",events:{input:e=>{const t=e.target;t.parentElement.previousElementSibling.value=t.value}},id:`${t.id}-value`,className:"other-val"},i=this.markup("input",null,n),a=[document.createTextNode(I.mi18n("other")),this.markup("input",null,o)],l=this.markup("label",a,{for:n.id}),u=this.markup("div",[i,l],{className:r});e.push(u)}}return this.dom="select"==i?this.markup(c,e,h(d,!0)):this.markup("div",e,{className:i}),this.dom}groupRequired(){const e=this.element.getElementsByTagName("input"),t=this.element.querySelectorAll("input:not([type=text])"),r=this.element.querySelector(".other-option"),n=this.element.querySelector(".other-val"),o=()=>{const e=[].some.call(t,(e=>e.checked));((e,t,r,n)=>{[].forEach.call(e,(e=>{n?e.removeAttribute("required"):e.setAttribute("required","required"),((e,t)=>{const r=I.mi18n("minSelectionRequired",1);t?e.setCustomValidity(""):e.setCustomValidity(r)})(e,n)})),t&&(t.checked?r.setAttribute("required","required"):r.removeAttribute("required"))})(t,r,n,e)};for(let t=e.length-1;t>=0;t--)e[t].addEventListener("change",o);o()}onRender(){if(this.config.userData){const t=this.config.userData.slice();"select"===this.config.type?e(this.dom).val(t).prop("selected",!0):this.config.type.endsWith("-group")&&("checkbox-group"===this.config.type&&this.dom.querySelectorAll("input[type=checkbox]").forEach((e=>{e.removeAttribute("checked")})),this.dom.querySelectorAll("input").forEach((e=>{if(!e.classList.contains("other-val")){for(let r=0;r<t.length;r++)if(e.value===t[r]){e.setAttribute("checked","checked"),t.splice(r,1);break}if(e.id.endsWith("-other")&&t.length>0){const r=this.dom.querySelector(`#${e.id}-value`);e.setAttribute("checked","checked"),r.value=e.value=t[0],r.style.display="inline-block"}}})))}}});class ee extends I{static get definition(){return{mi18n:{date:"dateField",file:"fileUpload"}}}build(){let{name:e}=this.config;e=this.config.multiple?`${e}[]`:e;const t=Object.assign({},this.config,{name:e});return this.dom=this.markup("input",null,t),this.dom}onRender(){this.config.userData&&e(this.dom).val(this.config.userData[0])}}I.register(["text","file","date","number"],ee),I.register(["text","password","email","color","tel"],ee,"text"),I.register(["date","time","datetime-local"],ee,"date"),I.register(["number","range"],ee,"number");const te=["value"];class re extends I{static get definition(){return{mi18n:{textarea:"textArea"}}}build(){const e=this.config,{value:t=""}=e,r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,te);return delete r.type,this.field=this.markup("textarea",this.parsedHtml(t),r),this.field}onRender(){this.config.userData&&e(this.field).val(this.config.userData[0])}on(t){return"prerender"==t&&this.preview?t=>{this.field&&(t=this.field),e(t).on("mousedown",(e=>{e.stopPropagation()}))}:super.on(t)}}I.register("textarea",re),I.register("textarea",re,"textarea");const ne=["value"];re.register("tinymce",class extends re{configure(){if(this.js=[],window.tinymce||this.js.push("https://cdnjs.cloudflare.com/ajax/libs/tinymce/4.9.11/tinymce.min.js"),this.classConfig.js){let e=this.classConfig.js;Array.isArray(e)||(e=new Array(e)),this.js=this.js.concat(e),delete this.classConfig.js}this.classConfig.css&&(this.css=this.classConfig.css),this.editorOptions={height:250,paste_data_images:!0,plugins:["advlist","autolink","lists","link","image","charmap","print","preview","anchor","searchreplace","visualblocks","code","fullscreen","insertdatetime","media","table","contextmenu","paste","code"],toolbar:"undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | table"}}build(){const e=this.config,{value:t=""}=e,r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ne);return delete r.type,this.field=this.markup("textarea",this.parsedHtml(t),r),r.disabled&&(this.editorOptions.readonly=!0),this.field}onRender(){const e=window.tinymce.get(this.id);e&&window.tinymce.remove(e);const t=jQuery.extend(this.editorOptions,this.classConfig);t.target=this.field;const r=[];Number(window.tinymce.majorVersion)>=5&&r.push("contextmenu"),Number(window.tinymce.majorVersion)>=6&&r.push("paste","print"),t.plugins=t.plugins.filter((e=>-1===r.indexOf(e)));const n=this.config.userData?this.parsedHtml(this.config.userData[0]):void 0,o=window.lastFormBuilderCopiedTinyMCE?this.parsedHtml(window.lastFormBuilderCopiedTinyMCE):void 0;window.lastFormBuilderCopiedTinyMCE=null;const i=function(e){o?e[0].setContent(o):n&&e[0].setContent(n)};setTimeout((()=>{window.tinymce.init(t).then(i)}),0)}},"textarea");const oe=["value"];function ie(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function se(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ie(Object(r),!0).forEach((function(t){ae(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ie(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ae(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}re.register("quill",class extends re{configure(){const e={modules:{toolbar:[[{header:[1,2,!1]}],["bold","italic","underline"],["code-block"]]},placeholder:this.config.placeholder||"",theme:"snow"},[t,r]=z.splitObject(this.classConfig,["css","js"]);Object.assign(this,se(se({},{js:"https://cdn.quilljs.com/1.2.4/quill.js",css:"https://cdn.quilljs.com/1.2.4/quill.snow.css"}),t)),this.editorConfig=se(se({},e),r)}build(){const e=this.config,{value:t=""}=e,r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,oe);return delete r.type,this.field=this.markup("div",null,r),this.field.classList.contains("form-control")&&this.field.classList.remove("form-control"),this.field}onRender(e){const t=this.config.value||"",r=window.Quill.import("delta");window.fbEditors.quill[this.id]={};const n=window.fbEditors.quill[this.id];return n.instance=new window.Quill(this.field,this.editorConfig),n.data=new r,t&&n.instance.setContents(window.JSON.parse(this.parsedHtml(t))),n.instance.on("text-change",(function(e){n.data=n.data.compose(e)})),e}},"textarea"),r().addLanguage("en-US",{NATIVE_NAME:"English (US)",ENGLISH_NAME:"English",addOption:"Add Option +",allFieldsRemoved:"All fields were removed.",allowMultipleFiles:"Allow users to upload multiple files",allowSelect:"Allow selection",autocomplete:"Autocomplete",button:"Button",cannotBeEmpty:"This field cannot be empty",checkboxGroup:"Checkbox Group",checkbox:"Checkbox",checkboxes:"Checkboxes",className:"Class",clearAllMessage:"Are you sure you want to clear all fields?",clear:"Clear",close:"Close",content:"Content",copy:"Copy To Clipboard",copyButton:"&#43;",copyButtonTooltip:"Copy",dateField:"Date Field",description:"Help Text",descriptionField:"Description",devMode:"Developer Mode",editNames:"Edit Names",editorTitle:"Form Elements",editXML:"Edit XML",enableOther:"Enable &quot;Other&quot;",enableOtherMsg:"Let users enter an unlisted option",fieldDeleteWarning:"false",fieldVars:"Field Variables",fieldNonEditable:"This field cannot be edited.",fieldRemoveWarning:"Are you sure you want to remove this field?",fileUpload:"File Upload",formUpdated:"Form Updated",getStarted:"Drag a field from the right to this area",header:"Header",hide:"Edit",hidden:"Hidden Input",inline:"Inline",inlineDesc:"Display {type} inline",label:"Label",labelEmpty:"Field Label cannot be empty",limitRole:"Limit access to one or more of the following roles:",mandatory:"Mandatory",maxlength:"Max Length",minOptionMessage:"This field requires a minimum of 2 options",minSelectionRequired:"Minimum {min} selections required",multipleFiles:"Multiple Files",name:"Name",no:"No",noFieldsToClear:"There are no fields to clear",number:"Number",off:"Off",on:"On",option:"Option",optionCount:"Option {count}",options:"Options",optional:"optional",optionLabelPlaceholder:"Label",optionValuePlaceholder:"Value",optionEmpty:"Option value required",other:"Other",paragraph:"Paragraph",placeholder:"Placeholder","placeholders.value":"Value","placeholders.label":"Label","placeholders.email":"Enter your email","placeholders.className":"space separated classes","placeholders.password":"Enter your password",preview:"Preview",radioGroup:"Radio Group",radio:"Radio",removeMessage:"Remove Element",removeOption:"Remove Option",remove:"&#215;",required:"Required",reset:"Reset",requireValidOption:"Only accept a pre-defined Option",richText:"Rich Text Editor",roles:"Access",rows:"Rows",save:"Save",selectOptions:"Options",select:"Select",selectColor:"Select Color",selectionsMessage:"Allow Multiple Selections",size:"Size",sizes:"Sizes","size.xs":"Extra Small","size.sm":"Small","size.m":"Default","size.lg":"Large",step:"Step",style:"Style",styles:"Styles","styles.btn":"Button Styles","styles.btn.default":"Default","styles.btn.danger":"Danger","styles.btn.info":"Info","styles.btn.primary":"Primary","styles.btn.success":"Success","styles.btn.warning":"Warning",submit:"Submit",subtype:"Type",text:"Text Field",textArea:"Text Area",toggle:"Toggle",warning:"Warning!",value:"Value",viewJSON:"[{&hellip;}]",viewXML:"&lt;/&gt;",yes:"Yes"});const le={location:"assets/lang/"};n(700);class de{constructor(e={},t=[]){this.customRegister={},this.templateControlRegister={},this.def={icon:{},i18n:{}},this.register(e,t)}register(e={},t=[]){const n=r().locale;this.def.i18n[n]||(this.def.i18n[n]={});const o=this;Object.keys(e).forEach((t=>{const r=function(r,n){this.customControl=new W(r,n,e[t]),this.build=function(){return this.customControl.build()},this.on=function(e){return this.customControl.on(e)}};r.definition={},r.label=e=>o.label(e),r.icon=e=>o.icon(e),this.templateControlRegister[t]=r}));for(const o of t){let t=o.type;if(o.attrs=o.attrs||{},!t){if(!o.attrs.type){I.error("Ignoring invalid custom field definition. Please specify a type property.");continue}t=o.attrs.type}let i=o.subtype||t;if(e[t]){const e=this.templateControlRegister[t];e.definition=o,this.customRegister[i]=jQuery.extend(o,{type:t,class:e})}else try{const e=I.getClass(t,o.subtype);i=o.datatype?o.datatype:`${t}-${Math.floor(9e3*Math.random()+1e3)}`,this.customRegister[i]=jQuery.extend(o,{type:t,class:e})}catch(e){I.error("Error while registering custom field: "+t+(o.subtype?":"+o.subtype:"")+". Unable to find any existing defined control or template for rendering.")}this.def.i18n[n][i]=Array.isArray(o.label)?r().get(...o.label)||o.label[0]:o.label,this.def.icon[i]=o.icon}}label(e){const t=this.def;let n=t.i18n||{};n=n[r().locale]||n.default||n;const o=I.camelCase(e),i="object"==typeof n?n[o]||n[e]:n;if(i)return i;{let n=t.mi18n;return"object"==typeof n&&(n=n[o]||n[e]),n||(n=o),r().get(n)}}get definition(){return{}}icon(e){const t=this.def;return t&&"object"==typeof t.icon?t.icon[e]:t.icon}getRegistered(e=!1){var t;return e?null!==(t=this.templateControlRegister[e])&&void 0!==t?t:void 0:Object.keys(this.customRegister)}getClass(e){var t;return null!==(t=this.templateControlRegister[e])&&void 0!==t?t:void 0}lookup(e){return this.customRegister[e]}}class ce{constructor(e={}){const t={layout:V,layoutTemplates:{},controls:{},controlConfig:{},container:!1,dataType:"json",disableHTMLLabels:!1,formData:[],i18n:Object.assign({},le),messages:{formRendered:"Form Rendered",noFormData:"No form data.",other:"Other",selectColor:"Select Color",invalidControl:"Invalid control"},onRender:()=>{},render:!0,sanitizerOptions:{clobberingProtection:{document:!0,form:!1,namespaceAttributes:!0},backendOrder:["dompurify","sanitizer","fallback"]},templates:{},notify:{error:e=>{console.log(e)},success:e=>{console.log(e)},warning:e=>{console.warn(e)}}};this.options=jQuery.extend(!0,t,e),this.instanceContainers=[],(e=>{if("object"!=typeof e)throw"Invalid value given to setSanitizerConfig, expected config object";if(e.hasOwnProperty("clobberingProtection")&&["document","form","namespaceAttributes"].forEach((t=>{e.clobberingProtection.hasOwnProperty(t)&&"boolean"==typeof e.clobberingProtection[t]&&(o.clobberingProtection[t]=e.clobberingProtection[t])})),e.hasOwnProperty("backends")){if("object"!=typeof e.backends)throw"backends config expected to be an Object";Object.keys(e.backends).forEach((t=>o.backends[t]=e.backends[t]))}if(e.hasOwnProperty("backendOrder")){if(o.backendOrder=[],!Array.isArray(e.backendOrder))throw"backendOrder config expected to be an Array of backend keys as strings";e.backendOrder.forEach((e=>{if(!o.backends.hasOwnProperty(e))throw"unknown sanitizer backend "+e;o.backendOrder.push(e)}))}})(this.options.sanitizerOptions),r().current||r().init(this.options.i18n),this.options.formData?this.options.formData=this.parseFormData(this.options.formData):this.options.formData=[],I.controlConfig=e.controlConfig||{},I.loadCustom(e.controls),this.templatedControls=new de(this.options.templates),"function"!=typeof Element.prototype.appendFormFields&&(Element.prototype.appendFormFields=function(e){Array.isArray(e)||(e=[e]);const t=z.markup("div",e,{className:"rendered-form formbuilder-embedded-bootstrap"});this.appendChild(t),e.forEach((e=>{const[r]=e.className.match(/row-([^\s]+)/)||[];if(r){const n=this.id?`${this.id}-row-${r}`:`row-${r}`;let o=document.getElementById(n);o||(o=z.markup("div",null,{id:n,className:"row"}),t.appendChild(o)),o.appendChild(e)}else t.appendChild(e);e.dispatchEvent($.fieldRendered)}))}),"function"!=typeof Element.prototype.emptyContainer&&(Element.prototype.emptyContainer=function(){const e=this;for(;e.lastChild;)e.removeChild(e.lastChild)})}sanitizeField(e,t){let r=Object.assign({},e);return t&&(r.id=e.id&&`${e.id}-${t}`,r.name=e.name&&`${e.name}-${t}`),r.className=Array.isArray(e.className)?z.unique(e.className.join(" ").split(" ")).join(" "):e.className||e.class||null,delete r.class,e.values&&(r.values=e.values.map((e=>z.trimObj(e)))),r=z.trimObj(r),Array.isArray(e.userData)&&0===e.userData.length&&(r.userData=[]),r}getElement(e){return(e=this.options.container||e)instanceof jQuery?e=e[0]:"string"==typeof e&&(e=document.querySelector(e)),e}render(e=null,t=0){const r=this,n=this.options;e=this.getElement(e);const o=[],i=new n.layout(n.layoutTemplates,!1,n.disableHTMLLabels,n.controlConfig);if(n.formData.length)for(let e=0;e<n.formData.length;e++){const r=n.formData[e],s=this.sanitizeField(r,t),a=this.templatedControls.getClass(r.type)||I.getClass(r.type,r.subtype),l=i.build(a,s);o.push(l)}else n.notify.warning(n.messages.noFormData);if(e&&(this.instanceContainers[t]=e),n.render&&e)e.emptyContainer(),e.appendFormFields(o),n.onRender&&n.onRender(),n.notify.success(n.messages.formRendered);else{const e=e=>e.map((e=>e.innerHTML)).join("");r.markup=e(o)}if(!0===n.disableInjectedStyle){const e=document.getElementsByClassName("formBuilder-injected-style");L(e,(t=>(e=>{e.parentNode&&e.parentNode.removeChild(e)})(e[t])))}else"bootstrap"===n.disableInjectedStyle&&n.render&&e&&e.getElementsByClassName("formbuilder-embedded-bootstrap").item(0)?.classList.remove("formbuilder-embedded-bootstrap");return r}renderControl(e=null){const t=this.options,r=t.formData;if(!r||Array.isArray(r))throw new Error("To render a single element, please specify a single object of formData for the field in question");const n=this.sanitizeField(r),o=new t.layout,i=this.templatedControls.getClass(r.type)||I.getClass(r.type,r.subtype),s=t.forceTemplate||"hidden",a=o.build(i,n,s);return e.appendFormFields(a),t.notify.success(t.messages.formRendered),this}get userData(){const t=this.options.formData.slice();return t.filter((e=>"tinymce"===e.subtype)).forEach((e=>window.tinymce.get(e.name).save())),this.instanceContainers.forEach((r=>{const n=e("select, input, textarea",r).serializeArray().reduce(((e,{name:t,value:r})=>(e[t=t.replace("[]","")]?e[t].push(r):e[t]=[r],e)),{}),o=t.length;for(let e=0;e<o;e++){var i;const r=t[e];void 0!==r.name&&(r.disabled||(r.userData=null!==(i=n[r.name])&&void 0!==i?i:[]))}})),t}clear(){this.instanceContainers.forEach((e=>{this.options.formData.slice().filter((e=>"tinymce"===e.subtype)).forEach((e=>window.tinymce.get(e.name).setContent(""))),e.querySelectorAll("input, select, textarea").forEach((e=>{["checkbox","radio"].includes(e.type)?e.checked=!1:e.value=""}))}))}parseFormData(e){const t={xml:e=>E(e),json:e=>window.JSON.parse(e)};return"object"!=typeof e&&(e=t[this.options.dataType](e)||!1),e}}!function(e){let t;const r={init:(e,n={})=>(t=e,r.instance=new ce(n),e.each((t=>r.instance.render(e[t],t))),r.instance),userData:()=>r.instance&&r.instance.userData,clear:()=>r.instance&&r.instance.clear(),setData:e=>{if(r.instance){const t=r.instance;t.options.formData=t.parseFormData(e)}},render:(e,n={})=>{if(r.instance){const o=r.instance;e||(e=o.options.formData),o.options=Object.assign({},o.options,n,{formData:o.parseFormData(e)}),t.each((e=>r.instance.render(t[e],e)))}},html:()=>t.map((e=>t[e])).html()};e.fn.formRender=function(e={},...t){if(r[e])return r[e].apply(this,t);{const t=r.init(this,e);return Object.assign(r,t),t}},e.fn.controlRender=function(e,t={}){t.formData=e,t.dataType="string"==typeof e?"json":"xml";const r=new ce(t),n=this;return n.each((e=>r.renderControl(n[e]))),n}}(jQuery)}()}()}(jQuery);